import os
import sys
import warnings
from datetime import datetime
from functools import lru_cache
from typing import Dict, Optional, Tuple, List
from collections import defaultdict

import pandas as pd
import pandas_market_calendars as mcal
import typer
from time import sleep

warnings.filterwarnings("ignore")
import queue
import threading
import traceback
import concurrent.futures
import signal

import os
from loguru import logger
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)
# retention="30 days" # 保留30天的日志


# Global flag to indicate if the program is being terminated
is_terminating = False

# Signal handler function
def handle_termination_signal(signum, frame):
    """Handle termination signals (SIGTERM, SIGINT)"""
    global is_terminating
    logger.warning(f"收到终止信号 {signum}，正在优雅关闭...")
    is_terminating = True
    # 不在这里调用 stop，让主线程处理清理工作

# Register signal handlers
signal.signal(signal.SIGTERM, handle_termination_signal)
signal.signal(signal.SIGINT, handle_termination_signal)

# 引入vnpy和数据库
from vnpy.trader.utility import get_file_path, ZoneInfo
from vnpy.trader.setting import SETTINGS
from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.object import BarData
from vnpy.trader.database import DB_TZ
# ALTER TABLE dbbardata MODIFY COLUMN id BIGINT AUTO_INCREMENT;

# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)


# 引入富途复权因子
from utils.database_manager import FutuRehab, db_manager

from utils.mysql_database import create_mysql_database

# 导入get_latest_conid中的NA_VALUES
from utils.mixin import NA_VALUES

# 时区定义
ET_TZ = ZoneInfo("America/New_York")  # 美东时间

class DatabaseSaver:
    """数据库保存管理器，使用多线程处理数据保存"""

    def __init__(self, database, num_threads=10):
        """初始化"""
        self.queue = queue.Queue()  # 使用单个队列处理所有线程的任务
        self.mysql_database = database
        self.active = False
        self.threads = [
            threading.Thread(target=self._save_worker, args=(i,), daemon=True)
            for i in range(num_threads)
        ]
        self._processed_count = 0
        self._total_count = 0
        self._lock = threading.Lock()  # 用于保护计数器

    def start(self):
        """启动保存线程"""
        if not self.active:
            self.active = True
            for thread in self.threads:
                thread.start()

    def stop(self):
        """停止保存线程"""
        if self.active:
            # 发送停止信号
            for _ in self.threads:
                self.queue.put(None)

            # 等待队列中的所有任务处理完成
            logger.info("等待数据保存完成...")
            self.queue.join()
            logger.info("所有数据已处理完成")

            self.active = False

            # 等待所有线程结束
            for thread in self.threads:
                if thread.is_alive():
                    thread.join()

            logger.success(f"数据保存已完成，共处理 {self._processed_count} 条数据")

    def put_bars(self, bars: List[BarData], symbol: str = None):
        """添加K线数据到保存队列"""
        if not self.active:
            self.start()

        with self._lock:
            self._total_count += len(bars)

        # 如果队列中的任务超过一定数量，则阻塞等待
        while self.queue.qsize() >= 100:
            logger.info(f"队列中任务数量已达限制，等待处理... 当前队列大小: {self.queue.qsize()}")
            sleep(1)  # 等待1秒

        self.queue.put((bars, symbol))

    def _save_worker(self, worker_id: int):
        """保存工作线程"""
        while self.active:
            try:
                data = self.queue.get(timeout=1)

                if data is None:
                    self.queue.task_done()
                    break

                bars, symbol = data

                # 添加重试逻辑，不限制重试次数
                retry_count = 0
                while True:
                    try:
                        self.mysql_database.save_bar_data(bars)
                        break  # 成功则跳出重试循环
                    except Exception as e:
                        # 检查是否是死锁错误或其他需要重试的错误
                        # if "Deadlock found when trying to get lock" in error_str or "Lock wait timeout exceeded" in error_str:
                        if 'lock' in str(e).lower():
                            retry_count += 1
                            wait_time = min(0.5 * (2 ** retry_count), 30)  # 指数退避策略，最大等待30秒
                            logger.warning(f"线程 {worker_id} 遇到数据库锁错误，等待 {wait_time:.2f} 秒后第 {retry_count} 次重试...")
                            sleep(wait_time)

                            # 重新创建数据对象
                            for bar in bars:
                                bar.exchange = Exchange(bar.exchange)
                                bar.interval = Interval(bar.interval)
                                bar.gateway_name = None
                                bar.vt_symbol = None
                        else:
                            # 非死锁错误直接抛出
                            raise

                with self._lock:
                    self._processed_count += len(bars)

                # 打印进度
                if symbol:
                    logger.info(f"线程 {worker_id} 已保存 {symbol} 的 {len(bars)} 条数据，总进度: {self._processed_count}/{self._total_count}")

                self.queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"保存数据时出错: {str(e)}\n{traceback.format_exc()}")


class FirstratePreprocessor:
    """FirstRate数据预处理器，负责数据筛选、时间对齐、复权等操作"""

    def __init__(self, data_dir="S:\\firstrate\\stock", data_path="full_1min", start_date: Optional[datetime] = None,
                 end_date: Optional[datetime] = None, db_threads=10, process_threads=10, ignore_overview=False,
                 database_name: Optional[str] = None):
        """初始化

        Args:
            data_dir: FirstRate数据所在的基础目录
            data_path: FirstRate数据文件夹名称，默认为"full_1min"，可选"month_1min"等
            start_date: 开始日期，只处理该日期之后的数据
            end_date: 结束日期，只处理该日期及之前的数据
            db_threads: 数据库保存线程数，默认为10
            process_threads: 标的处理线程数，默认为10
            ignore_overview: 是否忽略数据库中的overview信息，强制全量覆盖
            database_name: 数据库名称，如果为None则使用默认值'vnpy_stk_us_frt'
        """
        # 文件路径定义
        self.base_data_dir = data_dir
        self.data_path = data_path
        self.firstrate_dir = os.path.join(self.base_data_dir, self.data_path)
        self.start_date = start_date
        self.end_date = end_date
        self.process_threads = process_threads
        self.ignore_overview = ignore_overview

        # 创建数据库连接
        settings = SETTINGS.copy()
        if database_name:
            settings['database.database'] = database_name
        else:
            settings['database.database'] = 'vnpy_stk_us_frt'
        logger.info(f"使用数据库: {settings['database.database']}")
        
        # 创建数据库实例和获取Model类
        self.database, self.DbBarData, self.DbTickData, self.DbBarOverview, self.DbTickOverview = create_mysql_database(settings, replace_on_conflict=False)

        self.nyse = mcal.get_calendar('NYSE')

        # 匹配文件
        self.matched_file = f'matched_symbols_{data_path}.csv'

        # 加载匹配的标的
        self.matched_df = self._load_matched_symbols()

        # 加载扫描器数据用于过滤
        self.scanner_df = self._load_scanner_data()

        # 缓存所有标的前复权因子
        self.rehab_factors = {}

        # 显示时区信息
        logger.info(f"数据库时区: {DB_TZ}")
        logger.info(f"使用的美东时区: {ET_TZ}")
        if start_date:
            logger.info(f"只处理 {start_date.strftime('%Y-%m-%d')} 之后的数据")
        if end_date:
            logger.info(f"只处理 {end_date.strftime('%Y-%m-%d')} 及之前的数据")

        # 创建数据保存管理器并启动
        self.database_saver = DatabaseSaver(self.database, db_threads)
        self.database_saver.start()
        self.overviews = [] if ignore_overview else self.database_saver.mysql_database.get_bar_overview()
        
        logger.info(f"数据库保存线程数: {db_threads}, 标的处理线程数: {process_threads}")

    def _load_matched_symbols(self) -> pd.DataFrame:
        """加载匹配后的标的列表"""
        matched_file_path = get_file_path(self.matched_file)

        if not os.path.exists(matched_file_path):
            logger.error(f"匹配文件 {matched_file_path} 不存在，请先运行 get_latest_conid.py")
            return pd.DataFrame()

        matched_df = pd.read_csv(
            matched_file_path,
            na_values=NA_VALUES,
            keep_default_na=False
        )

        logger.info(f"已加载 {len(matched_df)} 条匹配记录")
        return matched_df

    def _load_scanner_data(self) -> pd.DataFrame:
        """加载扫描器数据用于过滤活跃股票"""
        scanner_file_path = get_file_path("scanner_unique_stk_us_all.csv")
        
        if not os.path.exists(scanner_file_path):
            logger.warning(f"扫描器文件 {scanner_file_path} 不存在，将处理所有匹配的标的")
            return pd.DataFrame()
        
        try:
            scanner_df = pd.read_csv(
                scanner_file_path,
                na_values=NA_VALUES,
                keep_default_na=False
            )
            logger.info(f"已加载扫描器数据 {len(scanner_df)} 条记录")
            return scanner_df
        except Exception as e:
            logger.error(f"加载扫描器数据失败: {str(e)}")
            return pd.DataFrame()

    @lru_cache(maxsize=999)
    def generate_trading_minutes(self, start_date_str: str, end_date_str: str) -> List[pd.Timestamp]:
        """生成指定日期范围内的交易分钟时间戳"""
        # 美股交易日历
        schedule = self.nyse.schedule(start_date=start_date_str, end_date=end_date_str)
        if schedule.empty:
            return []

        minutes = mcal.date_range(schedule, frequency='1Min', closed='left', force_close=False)
        return [ts.astimezone(ET_TZ) for ts in minutes]

    def load_firstrate_data(self, symbol: str, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> pd.DataFrame:
        """加载FirstRate数据

        Args:
            symbol: FirstRate标的代码
            start_date: 开始日期，只处理该日期之后的数据
            end_date: 结束日期，只处理该日期及之前的数据
        """
        file_path = os.path.join(self.firstrate_dir, f"{symbol}_{self.data_path}_UNADJUSTED.txt")

        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {file_path}")
            return pd.DataFrame()

        # 读取数据，格式：DateTime,Open,High,Low,Close,Volume
        df = pd.read_csv(
            file_path,
            header=None,
            names=["datetime", "open", "high", "low", "close", "volume"],
            parse_dates=["datetime"],
            na_values=NA_VALUES,
            keep_default_na=False
        )

        # 规范化日期时间格式，FirstRate数据默认是美东时间
        df["datetime"] = pd.to_datetime(df["datetime"]).dt.tz_localize(ET_TZ)

        # 如果设置了开始日期，先过滤数据
        if start_date:
            if end_date:
                df = df[(df["datetime"] >= start_date) & (df["datetime"] <= end_date)]
            else:
                df = df[df["datetime"] >= start_date]

            if df.empty:
                logger.warning(f"该时间段内无数据: {symbol}")
                return df

        # 在设置索引前处理重复时间戳，保留第一条记录
        df = df.drop_duplicates(subset=["datetime"], keep="first")

        # 设置索引
        df.set_index("datetime", inplace=True)

        return df

    def process_symbol(self, row: pd.Series) -> bool:
        """处理单个标的数据"""
        conid = row['conid']
        ib_symbol = row['ib_symbol']
        firstrate_symbol = row['firstrate_symbol']

        logger.info(f"处理标的: {ib_symbol} (conid: {conid}), FirstRate标的: {firstrate_symbol}")

        # 1. 加载数据
        df = self.load_firstrate_data(firstrate_symbol, self.start_date, self.end_date)
        if df.empty:
            logger.warning(f"无法加载FirstRate数据: {firstrate_symbol}")
            return False

        # 2. 获取数据的日期范围并生成交易分钟
        if self.data_path.split('_')[1] != '1day':
            trading_minutes = self.generate_trading_minutes(
                df.index.min().strftime("%Y-%m-%d"),
                df.index.max().strftime("%Y-%m-%d")
            )

            if not trading_minutes:
                logger.warning(f"该时间段内无交易日: {firstrate_symbol}")
                return False

            # 3. 使用完整时间索引重新索引数据
            full_df = df.reindex(trading_minutes)

            # 4. 按日期分组填充价格，按照严格顺序：
            # 4.1 首先对close进行前向填充 (ffill)
            full_df['close'] = full_df.groupby(full_df.index.date)['close'].ffill()

            # 4.2 用close填充OHL（必须先于open bfill，保证中间缺的是ffill的）
            for col in ['open', 'high', 'low']:
                full_df[col] = full_df[col].fillna(full_df['close'])

            # 4.3 对open进行后向填充 (bfill)
            full_df['open'] = full_df.groupby(full_df.index.date)['open'].bfill()

            # 4.4 用open填充HLC
            for col in ['high', 'low', 'close']:
                full_df[col] = full_df[col].fillna(full_df['open'])

        else:
            full_df = df

        # 5. 删除开盘初期没有数据的行（收盘价为NaN的行）
        full_df = full_df.dropna(subset=['close'])

        if full_df.empty:
            logger.warning(f"处理后无有效数据: {firstrate_symbol}")
            return False

        # 6. 用收盘价填充其他价格字段
        if self.data_path.split('_')[1] == '1day':
            for col in ['open', 'high', 'low']:
                full_df[col] = full_df[col].fillna(full_df['close'])

        # 7. 成交量用0填充
        full_df['volume'] = full_df['volume'].fillna(0)

        # 8. 转换为BarData对象
        all_bars = []
        interval = Interval.DAILY if self.data_path.split('_')[1] == '1day' else Interval.MINUTE
        for row in full_df.itertuples():
            bar = BarData(
                symbol=str(conid),
                exchange=Exchange.SMART,
                datetime=row.Index.to_pydatetime(),
                interval=interval,
                volume=float(row.volume),
                open_price=float(row.open),
                high_price=float(row.high),
                low_price=float(row.low),
                close_price=float(row.close),
                turnover=0.0,
                open_interest=0.0,
                gateway_name="FIRSTRATE"
            )
            all_bars.append(bar)

        # 9. 保存到数据库
        if all_bars:
            self.database_saver.put_bars(all_bars, ib_symbol)
            logger.info(f"已将数据加入保存队列: {ib_symbol} (conid: {conid}), 数据量: {len(all_bars)}")
            return True
        else:
            logger.warning(f"没有数据需要保存: {ib_symbol} (conid: {conid})")
            return False

    def process_all(self, conid_list: Optional[List[int]] = None, start_conid: Optional[int] = None,
                   n: Optional[int] = None) -> Tuple[int, int, List[int], List[int]]:
        """处理所有匹配的标的

        Args:
            conid_list: 要处理的conid列表，如果为None则处理所有匹配的标的
            start_conid: 从指定的conid开始处理，用于断点恢复
            n: 处理第1到n个conid（不包含第n个）

        Returns:
            Tuple[int, int, List[int], List[int]]: (成功数量, 失败数量, 成功的conid列表, 失败的conid列表)
        """
        if self.matched_df.empty:
            logger.warning("没有找到匹配的标的")
            return 0, 0, [], []

        # 如果指定了conid列表，只处理这些conid
        if conid_list:
            self.matched_df = self.matched_df[self.matched_df['conid'].isin(conid_list)]
            if self.matched_df.empty:
                logger.warning(f"未找到指定的conid: {conid_list}")
                return 0, 0, [], []
            logger.info(f"将处理指定的 {len(self.matched_df)} 个标的")
        # 如果指定了start_conid，从该conid开始处理
        elif start_conid:
            start_idx = self.matched_df[self.matched_df['conid'] == start_conid].index
            if len(start_idx) > 0:
                self.matched_df = self.matched_df.loc[start_idx[0]:]
                logger.info(f"从conid {start_conid} 开始处理，剩余 {len(self.matched_df)} 个标的")
            else:
                logger.warning(f"未找到conid {start_conid}，将从头开始处理")
        # 如果指定了n，只处理前n个conid（不包含第n个）
        elif n is not None:
            # 保存第n个conid，用于后续输出
            next_conid = self.matched_df.iloc[n]['conid'] if n < len(self.matched_df) else None
            self.matched_df = self.matched_df.iloc[:n]
            logger.info(f"将处理前 {n} 个标的，共 {len(self.matched_df)} 个")
            # 如果有下一个conid，输出用于继续处理
            if next_conid:
                logger.info(f"继续处理后续 conid 的命令: -c {next_conid}")
        else:
            # 如果没有指定conid相关参数，且有扫描器数据，进行过滤
            if not self.scanner_df.empty:
                original_count = len(self.matched_df)
                
                # 获取扫描器中的conId和symbol集合
                scanner_conids = set(self.scanner_df['conId'].astype(str))
                scanner_symbols = set(self.scanner_df['symbol'].astype(str))
                
                # 过滤：conid在扫描器的conId列中 或者 ib_symbol在扫描器的symbol列中
                self.matched_df = self.matched_df[
                    (self.matched_df['conid'].astype(str).isin(scanner_conids)) |
                    (self.matched_df['ib_symbol'].astype(str).isin(scanner_symbols))
                ]
                
                filtered_count = len(self.matched_df)
                logger.info(f"根据扫描器数据过滤：原始 {original_count} 个标的，过滤后 {filtered_count} 个标的")
                
                if self.matched_df.empty:
                    logger.warning("过滤后没有匹配的活跃标的")
                    return 0, 0, [], []

        success_count = 0
        fail_count = 0
        successful_conids = []  # 记录成功的conid
        failed_conids = []  # 记录失败的conid和symbol

        # 是否使用串行处理模式
        use_serial_mode = False
        if len(self.matched_df) <= 3:  # 如果标的数量很少，使用串行模式
            use_serial_mode = True
            logger.info("标的数量较少，使用串行处理模式")

        # 串行处理每个标的
        if use_serial_mode:
            for _, row in self.matched_df.iterrows():
                # 检查是否收到终止信号
                if is_terminating:
                    logger.warning("检测到终止信号，停止提交新任务...")
                    break

                try:
                    if self.process_symbol(row):
                        success_count += 1
                        successful_conids.append(row['conid'])
                    else:
                        fail_count += 1
                        failed_conids.append((row['conid'], row['ib_symbol']))
                except Exception as e:
                    logger.error(f"处理标的时出错: {str(e)}\n{traceback.format_exc()}")
                    fail_count += 1
                    failed_conids.append((row['conid'], row['ib_symbol']))
        else:
            # 并行处理模式
            lock = threading.Lock()

            def process_worker(row):
                nonlocal success_count, fail_count
                thread_id = threading.current_thread().ident
                logger.info(f"线程 {thread_id} 开始处理标的: {row['ib_symbol']} (conid: {row['conid']})")
                try:
                    if self.process_symbol(row):
                        with lock:
                            success_count += 1
                            successful_conids.append(row['conid'])
                        logger.info(f"线程 {thread_id} 完成处理标的: {row['ib_symbol']} - 成功")
                    else:
                        with lock:
                            fail_count += 1
                            failed_conids.append((row['conid'], row['ib_symbol']))
                        logger.info(f"线程 {thread_id} 完成处理标的: {row['ib_symbol']} - 失败")
                except Exception as e:
                    logger.error(f"线程 {thread_id} 处理标的时出错: {str(e)}\n{traceback.format_exc()}")
                    with lock:
                        fail_count += 1
                        failed_conids.append((row['conid'], row['ib_symbol']))

            # 创建线程池
            num_threads = min(self.process_threads, len(self.matched_df))  # 最多self.process_threads个线程
            logger.info(f"创建线程池，线程数: {num_threads}, 待处理标的数: {len(self.matched_df)}")
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
                # 提交所有任务
                futures = []
                for _, row in self.matched_df.iterrows():
                    # 检查是否收到终止信号
                    if is_terminating:
                        logger.warning("检测到终止信号，停止提交新任务...")
                        break
                    futures.append(executor.submit(process_worker, row))
                
                logger.info(f"已提交 {len(futures)} 个任务到线程池")

                # 等待所有已提交任务完成
                completed_count = 0
                for future in concurrent.futures.as_completed(futures):
                    completed_count += 1
                    logger.info(f"已完成任务 {completed_count}/{len(futures)}")
                    # 只是等待任务完成，不提交新任务
                    pass

        # 等待所有数据保存完成
        self.database_saver.stop()

        logger.success(f"处理完成. 成功: {success_count}, 失败: {fail_count}")

        # 如果指定了n或者收到了终止信号，输出成功的conid列表
        if n is not None or is_terminating:
            logger.info("成功的conid列表:")
            logger.info(f"  {', '.join(str(conid) for conid in successful_conids)}")

        if failed_conids:
            logger.warning("失败的标的列表:")
            for conid, symbol in failed_conids:
                logger.warning(f"  - {symbol} (conid: {conid})")
            logger.info("失败的conid列表（用于重试）:")
            logger.info(f"--conid-list \"{','.join(str(conid) for conid, _ in failed_conids)}\"")

        return success_count, fail_count, successful_conids, [conid for conid, _ in failed_conids]


app = typer.Typer()

@app.command()
def process(
    data_path: str = typer.Option(
        "full_1min",
        "--data-path",
        "-p",
        help="数据路径，可选 full_1min 或 month_1min"
    ),
    data_dir: str = typer.Option(
        "S:\\firstrate\\stock",
        "--data-dir",
        "-d",
        help="FirstRate数据目录"
    ),
    start_date: datetime = typer.Option(
        datetime(2024, 3, 1),
        "--start-date",
        "-s",
        help="开始日期，格式：YYYY-MM-DD，只处理该日期之后的数据",
        formats=["%Y-%m-%d"]
    ),
    end_date: Optional[datetime] = typer.Option(
        None,
        "--end-date",
        "-e",
        help="结束日期，格式：YYYY-MM-DD，只处理该日期及之前的数据",
        formats=["%Y-%m-%d"]
    ),
    start_conid: Optional[int] = typer.Option(
        None,
        "--start-conid",
        "-c",
        help="从指定的conid开始处理，用于断点恢复"
    ),
    n: Optional[int] = typer.Option(
        None,
        "--n",
        "-n",
        help="处理第1到n个conid（不包含第n个）"
    ),
    conid_list: Optional[str] = typer.Option(
        None,
        "--conid-list",
        "-l",
        help="要处理的conid列表，用逗号分隔，例如：95514904,548309229,162225735,705140936,662521562,150304986,252184470"
    ),
    ignore_overview: bool = typer.Option(
        False,
        "--ignore-overview",
        "-i",
        help="忽略数据库中的overview信息，强制全量覆盖"
    ),
    db_threads: int = typer.Option(
        10,
        "--db-threads",
        "-dt",
        help="数据库保存线程数，默认为10"
    ),
    process_threads: int = typer.Option(
        10,
        "--process-threads",
        "-pt",
        help="标的处理线程数，默认为10"
    ),
    database_name: Optional[str] = typer.Option(
        None,
        "--database",
        "-db-name",
        help="数据库名称，如果不指定则使用默认值'vnpy_stk_us_frt'"
    )
):
    """FirstRate原始数据预处理工具
        python firstrate_download.py  -p day_1min
          get_latest_conid.py -p day_1min


    # 日线
    python firstrate_download.py -p day_1day
    python firstrate_download.py -f -p full_1day
    python update_conid.py -d vnpy_stk_us_frt
    python get_latest_conid.py -p full_1day
    ## 前复权
    python firstrate_preprocess_raw.py -p full_1day -s 2021-01-01 -e 2025-08-08 -db-name vnpy_stk_us_frt_d -pt 63

    # 分钟线
    python firstrate_download.py -p day_1min
    python firstrate_download.py -f -p full_1min
    python update_conid.py -d vnpy_stk_us_frt_m
    python get_latest_conid.py -p full_1min
    python firstrate_preprocess_raw.py -p day_1min -s 2021-01-01 -pt 63
    """
    try:
        # 创建预处理器
        processor = FirstratePreprocessor(
            data_dir=data_dir,
            data_path=data_path,
            start_date=start_date.replace(tzinfo=ET_TZ),
            end_date=end_date.replace(tzinfo=ET_TZ) if end_date else None,
            db_threads=db_threads,
            process_threads=process_threads,
            ignore_overview=ignore_overview,
            database_name=database_name
        )

        # 处理conid列表
        conid_list_to_process = None

        # 如果指定了conid_list，解析并处理
        if conid_list:
            try:
                conid_list_to_process = [int(conid.strip()) for conid in conid_list.split(',')]
                logger.info(f"将处理指定的conid列表: {conid_list_to_process}")
            except ValueError as e:
                logger.error(f"conid列表格式错误: {e}")
                return

        # 处理所有标的
        success, fail, successful_conids, failed_conids = processor.process_all(conid_list_to_process, start_conid, n)
        logger.success(f"处理完成. 成功: {success}, 失败: {fail}")

    except KeyboardInterrupt:
        logger.warning("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}\n{traceback.format_exc()}")
    finally:
        # 确保数据保存线程正确关闭
        if 'processor' in locals() and not is_terminating:
            # 只有在未收到终止信号时才调用stop，避免重复调用
            processor.database_saver.stop()

if __name__ == "__main__":
    app()
