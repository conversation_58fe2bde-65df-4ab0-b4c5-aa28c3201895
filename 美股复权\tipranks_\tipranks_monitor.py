#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import traceback
import requests
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional
from loguru import logger
import time
from peewee import chunked

import os, sys
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
# 导入企业微信通知模块
from utils.wecom_alert import WecomAlertManager
# 导入数据库管理模块
from utils.database_manager import db_manager, TipranksRehab

class TipranksMonitor:
    """TipRanks股票拆分监控器"""
    
    def __init__(self):
        """初始化监控器"""
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        # 设置截止日期，只获取2025-08-15之后的数据
        self.cutoff_date = datetime(2025, 8, 15)

        # 加载IB产品映射
        self.symbol_conid_map = self._load_ib_products()

        # API URLs
        self.splits_api_url = "https://www.tipranks.com/api/stocks/splits?sortDir=1&country=us&method=stockSplit&isFuture=true"
        self.historical_splits_url = "https://www.tipranks.com/calendars/stock-splits/historical"
        self.dividends_api_url = "https://www.tipranks.com/calendars/dividends/payload.json"

    def _load_ib_products(self) -> Dict[str, int]:
        """加载IB产品信息，建立symbol到conid的映射"""
        symbol_conid_map = {}
        try:
            from utils.database_manager import IbProduct
            query = IbProduct.select(IbProduct.symbol, IbProduct.conid).where(
                IbProduct.is_latest == True
            ).order_by(IbProduct.created_time.asc())
            for record in query:
                if record.symbol and record.conid:
                    symbol_conid_map[record.symbol] = record.conid
            logger.info(f"已加载{len(symbol_conid_map)}个IB产品映射关系")
        except Exception as e:
            logger.error(f"加载IB产品信息时出错: {str(e)}")
            logger.error(traceback.format_exc())
        return symbol_conid_map
    
    def _get_ib_conid(self, tipranks_symbol: str) -> int:
        """根据TipRanks的symbol获取IB的conid"""
        try:
            # 处理TipRanks的symbol格式
            # 先尝试：把.换成空格去搜索
            # 如果搜不到，再把原先.后面的字母前加上PR
            # 比如 ACP.A 先尝试 'ACP A'，搜不到再尝试 'ACP PRA'
            if '.' in tipranks_symbol:
                parts = tipranks_symbol.split('.')
                if len(parts) == 2:
                    base_symbol = parts[0]
                    suffix = parts[1]

                    # 方法1：直接替换点号为空格
                    ib_symbol_1 = f"{base_symbol} {suffix}"
                    conid = self.symbol_conid_map.get(ib_symbol_1)

                    if conid:
                        logger.debug(f"找到conid: {tipranks_symbol} -> {ib_symbol_1} -> {conid}")
                        return conid

                    # 方法2：在空格后的字母前加上'PR'
                    ib_symbol_2 = f"{base_symbol} PR{suffix}"
                    conid = self.symbol_conid_map.get(ib_symbol_2)

                    if conid:
                        logger.debug(f"找到conid: {tipranks_symbol} -> {ib_symbol_2} -> {conid}")
                        return conid

                    # 两种方法都没找到
                    logger.debug(f"未找到conid: {tipranks_symbol} -> 尝试了 {ib_symbol_1} 和 {ib_symbol_2}")
                    return None
                else:
                    return None
            else:
                # 没有点号，直接搜索
                conid = self.symbol_conid_map.get(tipranks_symbol)
                if conid:
                    logger.debug(f"找到conid: {tipranks_symbol} -> {conid}")
                return conid

        except Exception as e:
            logger.warning(f"获取conid失败: {tipranks_symbol}, 错误: {e}")
            return None

    def fetch_splits_data(self) -> List[Dict]:
        """获取当前股票拆分数据"""
        try:
            # 先访问主页获取cookies
            logger.info("先访问主页获取cookies...")
            main_page_response = self.session.get("https://www.tipranks.com/", timeout=30)
            if main_page_response.status_code == 200:
                logger.info("成功访问主页")
            else:
                logger.warning("访问主页失败，但继续尝试API")
            
            # 获取股票拆分数据
            logger.info(f"开始获取股票拆分数据: {self.splits_api_url}")
            response = self.session.get(self.splits_api_url, timeout=30)
            logger.info(f"API响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                
                if data and 'data' in data:
                    api_data = data['data']
                    logger.info(f"成功获取到 {len(api_data)} 条股票拆分记录")
                    
                    # 获取今天的日期作为上限
                    today = datetime.now()
                    logger.info(f"设置当前拆分数据日期上限: {today}")
                    
                    # 直接转换为数据库格式
                    db_records = []
                    for split in api_data:
                        try:
                            effective_date_str = split.get('effectiveDate')
                            if not effective_date_str:
                                continue
                            
                            effective_date = self.parse_effective_date(effective_date_str)
                            if not effective_date:
                                continue
                            
                            # 只保留不大于今天的数据
                            if effective_date <= today:
                                # 直接构建数据库格式的记录
                                db_record = {
                                    'symbol': split.get('ticker', ''),
                                    'description': split.get('companyName', ''),
                                    'ex_div_date': effective_date,
                                    'split_ratio': self.parse_split_ratio(split.get('splitRatio', '')),
                                    'dividend': None
                                }
                                
                                db_records.append(db_record)
                            
                        except Exception as e:
                            logger.warning(f"处理拆分记录失败: {split}, 错误: {e}")
                            continue
                    
                    logger.info(f"成功转换 {len(db_records)} 条拆分记录为数据库格式（过滤后）")
                    return db_records
                else:
                    logger.warning("API响应中没有找到数据")
                    return []
            else:
                logger.error(f"请求API失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"获取股票拆分数据失败: {e}")
            logger.error(traceback.format_exc())
            return []

    def fetch_comprehensive_historical_splits(self) -> List[Dict]:
        """获取完整的历史股票拆分数据"""
        try:
            logger.info("尝试通过API获取历史拆分数据...")
            
            logger.info(f"设置截止日期: {self.cutoff_date}")
            
            all_historical_data = []
            page = 1
            page_size = 50
            has_more_pages = True
            should_stop = False
            
            # 获取今天的日期作为上限
            today = datetime.now()
            logger.info(f"设置日期上限: {today}")
            
            while has_more_pages and not should_stop:
                # 构建分页API URL
                historical_api_url = f"https://www.tipranks.com/api/stocks/splits?sortDir=1&country=us&method=stockSplit&isFuture=false&page={page}&limit={page_size}"
                
                logger.info(f"获取历史数据第 {page} 页: {historical_api_url}")
                
                response = self.session.get(historical_api_url, timeout=30)
                logger.info(f"历史数据第 {page} 页API响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # 检查是否有数据
                    if 'data' not in data or not data['data']:
                        logger.info(f"第 {page} 页没有更多数据，停止获取")
                        break
                    
                    page_data = data['data']
                    current_page_count = len(page_data)
                    
                    # 检查这一页的日期范围
                    page_dates = []
                    for split in page_data:
                        try:
                            effective_date_str = split.get('effectiveDate')
                            if effective_date_str:
                                effective_date = self.parse_effective_date(effective_date_str)
                                if effective_date:
                                    page_dates.append(effective_date)
                        except:
                            continue
                    
                    if page_dates:
                        min_date = min(page_dates)
                        max_date = max(page_dates)
                        logger.info(f"第 {page} 页日期范围: {min_date} 到 {max_date}")
                        
                        # 如果这一页的最小日期都小于截止日期，说明后面的数据都更旧，可以停止
                        if min_date < self.cutoff_date:
                            logger.info(f"第 {page} 页最小日期 {min_date} 小于截止日期 {self.cutoff_date}，停止获取")
                            should_stop = True
                    
                    # 直接转换为数据库格式并过滤日期
                    for split in page_data:
                        try:
                            effective_date_str = split.get('effectiveDate')
                            if not effective_date_str:
                                continue
                            
                            effective_date = self.parse_effective_date(effective_date_str)
                            if not effective_date:
                                continue
                            
                            # 只保留指定日期之后且不大于今天的数据
                            if effective_date >= self.cutoff_date and effective_date <= today:
                                # 直接构建数据库格式的记录
                                db_record = {
                                    'symbol': split.get('ticker', ''),
                                    'description': split.get('companyName', ''),
                                    'ex_div_date': effective_date,
                                    'split_ratio': self.parse_split_ratio(split.get('splitRatio', '')),
                                    'dividend': None
                                }
                                
                                all_historical_data.append(db_record)
                            
                        except Exception as e:
                            logger.warning(f"处理历史拆分记录失败: {split}, 错误: {e}")
                            continue
                    
                    logger.info(f"第 {page} 页获取到 {current_page_count} 条记录，过滤后保留 {len([r for r in all_historical_data if r.get('ex_div_date') >= self.cutoff_date and r.get('ex_div_date') <= today])} 条")
                    
                    # 如果这一页的数据少于页面大小，说明已经是最后一页
                    if current_page_count < page_size:
                        logger.info(f"第 {page} 页数据量小于 {page_size}，已获取完所有历史数据")
                        has_more_pages = False
                    else:
                        page += 1
                        time.sleep(1)  # 避免请求过于频繁
                        
                else:
                    logger.error(f"获取历史数据第 {page} 页失败，状态码: {response.status_code}")
            
            # 最终过滤，确保只保留指定日期之后且不大于今天的数据
            filtered_data = [record for record in all_historical_data if record.get('ex_div_date') >= self.cutoff_date and record.get('ex_div_date') <= today]
            
            if filtered_data:
                logger.info(f"成功获取到 {len(filtered_data)} 条 {self.cutoff_date} 之后且不大于 {today} 的历史股票拆分记录")
                return filtered_data
            else:
                logger.warning(f"未获取到 {self.cutoff_date} 之后且不大于 {today} 的任何历史数据")
                return []
                
        except Exception as e:
            logger.error(f"获取完整历史股票拆分数据失败: {e}")
            logger.error(traceback.format_exc())
            return []


    def fetch_dividends_data(self) -> Optional[List[Dict]]:
        """获取分红数据 - 包括历史和未来数据"""
        try:
            logger.info("开始获取分红数据...")
            
            all_dividends_data = []
            
            # 获取从指定日期开始到今天的所有分红数据
            historical_dividends = self.fetch_comprehensive_dividends_data()

            if historical_dividends:
                all_dividends_data.extend(historical_dividends)
                logger.info(f"获取到 {len(historical_dividends)} 条历史分红数据")
            
            if all_dividends_data:
                logger.info(f"总计获取到 {len(all_dividends_data)} 条分红记录")
                return all_dividends_data
            else:
                logger.warning("未获取到任何分红数据")
                return None
                
        except Exception as e:
            logger.error(f"获取分红数据时发生错误: {e}")
            logger.error(traceback.format_exc())
            return None
    
    def fetch_comprehensive_dividends_data(self) -> List[Dict]:
        """获取从指定日期开始的所有分红数据"""
        try:
            logger.info("开始获取分红数据...")
            
            # 设置截止日期，只获取2025-08-15之后的数据
            self.cutoff_date = datetime(2025, 8, 15)
            logger.info(f"设置分红数据截止日期: {self.cutoff_date}")
            
            all_dividends = []
            
            # 从指定日期开始获取数据
            start_date = self.cutoff_date
            end_date = datetime.now()  # 到今天
            
            current_date = start_date
            total_days = (end_date - start_date).days + 1
            processed_days = 0
            
            logger.info(f"获取分红数据范围: {start_date} 到 {end_date} (共 {total_days} 天)")
            
            while current_date <= end_date:
                date_str = current_date.strftime('%Y-%m-%d')
                
                # 每处理10天显示一次进度
                if processed_days % 10 == 0:
                    progress = (processed_days / total_days) * 100
                    logger.info(f"分红数据获取进度: {progress:.1f}% ({processed_days}/{total_days}天)")
                
                daily_dividends = self.fetch_daily_dividends_by_date(date_str)

                if daily_dividends:
                    # 过滤日期，只保留指定日期之后的数据
                    filtered_dividends = []
                    for dividend in daily_dividends:
                        try:
                            ex_div_date = dividend.get('ex_div_date')
                            if ex_div_date:
                                # 解析日期进行比较
                                dividend_date = datetime.strptime(ex_div_date, '%Y-%m-%d')
                                if dividend_date >= self.cutoff_date:
                                    filtered_dividends.append(dividend)
                        except Exception as e:
                            logger.warning(f"过滤分红记录日期失败: {dividend}, 错误: {e}")
                            continue
                    
                    if filtered_dividends:
                        all_dividends.extend(filtered_dividends)
                        logger.debug(f"{date_str}: 获取到 {len(daily_dividends)} 条分红记录，过滤后保留 {len(filtered_dividends)} 条")
                
                current_date += timedelta(days=1)
                processed_days += 1
                
                # 每获取5天的数据后稍作休息，避免请求过于频繁
                if processed_days % 5 == 0:
                    time.sleep(0.3)
            
            logger.info(f"分红数据获取完成，总计获取 {len(all_dividends)} 条记录")
            return all_dividends
            
        except Exception as e:
            logger.error(f"获取全面分红数据时发生错误: {e}")
            logger.error(traceback.format_exc())
            return []
    
    def fetch_daily_dividends_by_date(self, date_str: str) -> List[Dict]:
        """根据日期获取当日的分红数据"""
        try:
            # 构建API URL
            api_url = f"https://www.tipranks.com/calendars/dividends/{date_str}/payload.json"
            
            response = self.session.get(api_url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                # 检查数据结构
                if 'data' not in data:
                    return []
                
                table_data = data['data'].get('tableData', [])
                if not table_data:
                    return []
                
                dividends_list = []
                for dividend_item in table_data:
                    try:
                        # 解析分红数据
                        ticker = dividend_item.get('ticker', '').strip()
                        name = dividend_item.get('name', '').strip()
                        dividend_info = dividend_item.get('dividend', {})
                        
                        # 提取分红金额和执行日期
                        amount = None
                        execution_date = date_str  # 默认使用请求的日期
                        
                        if isinstance(dividend_info, dict):
                            amount = dividend_info.get('amount', 0)
                            # 执行日期在dividend对象内部
                            execution_date_iso = dividend_info.get('executionDate', '')
                            if execution_date_iso:
                                # 转换ISO日期格式为YYYY-MM-DD
                                try:
                                    execution_date = execution_date_iso.split('T')[0]
                                except:
                                    execution_date = date_str
                        
                        # 只有当有有效数据时才添加记录
                        if ticker and amount is not None and amount > 0:
                            dividend_record = {
                                'symbol': ticker,
                                'description': name,
                                'ex_div_date': execution_date,
                                'split_ratio': None,
                                'dividend': float(amount)

                            }
                            dividends_list.append(dividend_record)

                    except Exception as item_error:
                        logger.warning(f"解析分红记录失败 ({date_str}): {item_error}")
                        continue
                
                return dividends_list
                
            elif response.status_code == 404:
                # 404表示该日期没有数据，这是正常的
                return []
            else:
                logger.warning(f"获取 {date_str} 分红数据失败，状态码: {response.status_code}")
                return []
                
        except Exception as e:
            logger.warning(f"获取 {date_str} 分红数据时发生错误: {e}")
            return []


    def deduplicate_splits_data(self, splits_data: List[Dict]) -> List[Dict]:
        """去重拆分数据"""
        seen = set()
        unique_data = []
        
        for split in splits_data:
            # 使用ticker和日期作为唯一标识
            key = (split.get('symbol', ''), split.get('ex_div_date', ''))
            if key not in seen:
                seen.add(key)
                unique_data.append(split)
        
        return unique_data

    def parse_effective_date(self, date_str: str) -> Optional[datetime]:
        """解析生效日期"""
        try:
            # API返回格式：2025-07-02T00:00:00
            return datetime.fromisoformat(date_str.replace('Z', '+00:00').replace('T00:00:00', ''))
        except Exception as e:
            logger.warning(f"解析日期失败: {date_str}, 错误: {e}")
            return None

    def parse_split_ratio(self, ratio_str: str) -> Optional[float]:
        """解析拆分比例"""
        try:
            if ratio_str is None or ratio_str == '未知比例':
                return None
            
            # 如果已经是数字类型，直接返回
            if isinstance(ratio_str, (int, float)):
                return float(ratio_str)
            
            # 转换为字符串处理
            ratio_str = str(ratio_str)
            
            # 处理格式如 "2:1", "3-for-1", "1:2" 等
            if ':' in ratio_str:
                parts = ratio_str.split(':')
                if len(parts) == 2:
                    numerator = float(parts[0].strip())
                    denominator = float(parts[1].strip())
                    return numerator / denominator
            elif '-for-' in ratio_str.lower():
                parts = ratio_str.lower().split('-for-')
                if len(parts) == 2:
                    numerator = float(parts[0].strip())
                    denominator = float(parts[1].strip())
                    return numerator / denominator
            else:
                # 直接转换为浮点数
                return float(ratio_str)
                
        except Exception as e:
            logger.warning(f"解析拆分比例失败: {ratio_str}, 错误: {e}")
            return None

    def save_to_database(self, db_records: List[Dict]) -> bool:
        """保存数据到数据库"""
        try:
            if not db_records:
                logger.info("没有数据需要保存")
                return True
            
            # 检查表是否存在，如果不存在则创建
            if not TipranksRehab.table_exists():
                logger.info("数据库表不存在，将创建新表")
                TipranksRehab.create_table()

                # 创建索引（Peewee的create_table不会自动创建索引）
                try:
                    # 创建联合唯一索引
                    index_sql = "CREATE UNIQUE INDEX idx_symbol_ex_div_date ON tipranks_rehab (symbol, ex_div_date)"
                    db_manager.common_db.execute_sql(index_sql)
                    logger.info("成功创建联合唯一索引 idx_symbol_ex_div_date")
                except Exception as e:
                    logger.warning(f"创建索引失败（可能已存在）: {e}")

            logger.info(f"开始保存 {len(db_records)} 条记录到数据库...")
            
            # 使用批量插入方式保存数据
            try:
                with db_manager.common_db.atomic():
                    for batch in chunked(db_records, 50):
                        TipranksRehab.insert_many(batch).on_conflict_replace().execute()

                logger.info("数据保存成功")
            except Exception as e:
                logger.error(f"批量插入数据失败: {e}")
                raise
            return True
            
        except Exception as e:
            logger.error(f"保存数据到数据库失败: {e}")
            logger.error(traceback.format_exc())
            return False

    def filter_today_splits(self, splits_data: List[Dict]) -> List[Dict]:
        """筛选今日生效的股票拆分"""
        today = datetime.now()
        today_splits = []
        
        logger.info(f"筛选 {today.date()} 生效的股票拆分记录")
        
        for split in splits_data:
            try:
                effective_date = split.get('ex_div_date')
                if not effective_date:
                    continue
                
                # 如果已经是datetime对象，直接使用；如果是字符串，则解析
                if isinstance(effective_date, str):
                    effective_date = self.parse_effective_date(effective_date)
                    if not effective_date:
                        continue
                
                # 比较日期
                if effective_date.date() == today.date():
                    today_splits.append(split)
                    
            except Exception as e:
                logger.error(f"处理股票拆分记录失败: {split}, 错误: {e}")
                continue
        
        logger.info(f"找到 {len(today_splits)} 条今日生效的股票拆分记录")
        return today_splits

    def filter_today_dividends(self, dividends_data: List[Dict]) -> List[Dict]:
        """筛选今日生效的分红"""
        today = datetime.now()
        today_dividends = []
        
        logger.info(f"筛选 {today.date()} 生效的分红记录")
        
        for dividend in dividends_data:
            try:
                ex_div_date = dividend.get('ex_div_date')
                if not ex_div_date:
                    continue
                
                # 如果已经是datetime对象，直接使用；如果是字符串，则解析
                if isinstance(ex_div_date, str):
                    effective_date = self.parse_effective_date(ex_div_date)
                    if not effective_date:
                        continue
                else:
                    effective_date = ex_div_date
                
                # 比较日期
                if effective_date.date() == today.date():
                    today_dividends.append(dividend)
                    
            except Exception as e:
                logger.error(f"处理分红记录失败: {dividend}, 错误: {e}")
                continue
        
        logger.info(f"找到 {len(today_dividends)} 条今日生效的分红记录")
        return today_dividends
    
    def format_alert_message(self, today_splits: List[Dict], today_dividends: List[Dict] = None) -> str:
        """格式化警报消息 - Markdown表格形式"""
        if not today_splits and not today_dividends:
            return ""
        
        # 统计数量
        forward_splits = [s for s in today_splits if s.get('split_ratio') and s.get('split_ratio') < 1]
        reverse_splits = [s for s in today_splits if s.get('split_ratio') and s.get('split_ratio') > 1]
        dividend_count = len(today_dividends) if today_dividends else 0

        # 构建markdown消息
        message_lines = [
            f"# {datetime.now().strftime('%Y-%m-%d')} TIPRANKS",
            "",
            f"今日拆股 {len(forward_splits)} 只，合股 {len(reverse_splits)} 只，分红 {dividend_count} 只",
            "",
            "| Symbol | Conid | Company Name | Split Ratio | Dividend |",
            "|---|---|---|---|---|"
        ]
        
        # 合并所有记录并排序
        all_records = []

        # 添加拆分记录
        for split in today_splits:
            symbol = split.get('symbol', '')
            conid = self._get_ib_conid(symbol)
            conid_str = str(conid) if conid else ''
            
            all_records.append({
                'symbol': symbol,
                'conid': conid_str,
                'description': split.get('description', ''),
                'split_ratio': split.get('split_ratio', ''),
                'dividend': split.get('dividend', ''),
                'ex_div_date': split.get('ex_div_date', ''),
                'type': 'split'
            })
        
        # 添加分红记录（如果还没有包含在拆分记录中）
        if today_dividends:
            for dividend in today_dividends:
                ticker = dividend.get('symbol', '')
                dividend_date = dividend.get('ex_div_date', '')

                # 检查是否已经存在于拆分记录中
                existing_record = any(
                    s.get('symbol') == ticker and s.get('ex_div_date') == dividend_date
                    for s in today_splits
                )

                if not existing_record:
                    conid = self._get_ib_conid(ticker)
                    conid_str = str(conid) if conid else ''

                    all_records.append({
                        'symbol': ticker,
                        'conid': conid_str,
                        'description': dividend.get('description', ''),
                        'split_ratio': dividend.get('split_ratio', ''),
                        'dividend': dividend.get('dividend', ''),
                        'ex_div_date': dividend_date,
                        'type': 'dividend'
                    })

        # 排序：先按类型分组（拆股、合股、分红），再按symbol字母顺序排序
        def sort_key(record):
            split_ratio = record.get('split_ratio')
            symbol = record.get('symbol', '')
            
            # 确定记录类型和优先级
            if split_ratio is not None:
                if split_ratio < 1:
                    # 拆股：优先级1，按symbol排序
                    return (1, symbol)
                else:
                    # 合股：优先级2，按symbol排序
                    return (2, symbol)
            else:
                # 分红：优先级3，按symbol排序
                return (3, symbol)

        # 应用排序
        all_records.sort(key=sort_key)
        
        # 生成表格行
        for record in all_records:
            ticker = record.get('symbol', '')
            conid = record.get('conid', '')
            company_name = record.get('description', '')
            split_ratio = record.get('split_ratio', '')
            dividend = record.get('dividend', '')

            # 格式化数据，处理None值
            ticker_str = ticker if ticker else ''
            conid_str = conid if conid else ''
            company_name_str = company_name if company_name else ''
            split_ratio_str = str(split_ratio) if split_ratio is not None else ''
            dividend_str = str(dividend) if dividend is not None else ''

            message_lines.append(f"| {ticker_str} | {conid_str} | {company_name_str} | {split_ratio_str} | {dividend_str} |")
        
        return "\n".join(message_lines)
    
    def send_alert(self, message: str) -> bool:
        """发送企业微信警报"""
        if not message:
            logger.info("没有今日生效的股票拆分或分红，不发送通知")
            return True
            
        try:
            logger.info("发送企业微信通知...")
            logger.info(f"通知内容:\n{message}")
            
            manager = WecomAlertManager(use_markdown_v2=True,key = 'ee0b6801-f2c5-4811-ba1f-227b543b3459')
            # manager = WecomAlertManager(use_markdown_v2=True,key = '379668d7-5d0f-42af-9a17-a2858426837c')
            manager.start()
            manager.add_message(message)
            manager.stop()
            
            logger.info("企业微信通知发送成功")
            return True
            
        except Exception as e:
            logger.error(f"发送企业微信通知失败: {e}")
            logger.error(traceback.format_exc())
            return False

    def merge_duplicate_records(self, all_records):
        """合并具有相同(symbol, ex_div_date)的记录"""
        logger.info("开始合并重复记录...")

        # 使用字典来合并记录，key为(symbol, ex_div_date)
        merged_dict = {}

        for record in all_records:
            symbol = record.get('symbol', '')
            ex_div_date = record.get('ex_div_date', '')

            if not symbol or not ex_div_date:
                logger.warning(f"跳过无效记录: {record}")
                continue

            key = (symbol, ex_div_date)

            if key in merged_dict:
                # 如果已经有记录，添加到待合并列表
                if isinstance(merged_dict[key], list):
                    merged_dict[key].append(record)
                else:
                    # 将现有记录转换为列表
                    merged_dict[key] = [merged_dict[key], record]
            else:
                # 新记录
                merged_dict[key] = record

        # 合并所有记录
        final_merged_records = []
        for key, records in merged_dict.items():
            if isinstance(records, list):
                # 多条记录需要合并
                merged_record = self.merge_multiple_records(records)
                final_merged_records.append(merged_record)
                logger.debug(f"合并了 {len(records)} 条记录: {key[0]} {key[1]}")
            else:
                # 单条记录直接添加
                final_merged_records.append(records)

        logger.info(f"合并完成: 原始 {len(all_records)} 条记录 -> 合并后 {len(final_merged_records)} 条记录")
        return final_merged_records

    def merge_multiple_records(self, records):
        """合并多条记录"""
        if not records:
            return None

        if len(records) == 1:
            return records[0].copy()

        logger.debug(f"开始合并 {len(records)} 条记录")

        # 初始化合并后的记录
        merged = records[0].copy()
        symbol = merged.get('symbol', '')
        ex_div_date = merged.get('ex_div_date', '')

        # 统计各种类型的记录
        split_records = [r for r in records if r.get('split_ratio') is not None]
        dividend_records = [r for r in records if r.get('dividend') is not None]

        logger.debug(f"记录 {symbol} {ex_div_date}: {len(split_records)} 条拆分记录, {len(dividend_records)} 条分红记录")

        # 处理split_ratio
        if len(split_records) > 1:
            # 如果有多条拆分记录，记录警告并保留第一个
            logger.warning(f"发现 {len(split_records)} 条重复的split_ratio: {symbol} {ex_div_date}")
            merged['split_ratio'] = split_records[0].get('split_ratio')
        elif len(split_records) == 1:
            merged['split_ratio'] = split_records[0].get('split_ratio')
        else:
            merged['split_ratio'] = None

        # 处理dividend - 累加所有分红
        if dividend_records:
            total_dividend = 0.0
            dividend_details = []

            for record in dividend_records:
                dividend = record.get('dividend')
                if isinstance(dividend, (int, float)):
                    total_dividend += dividend
                    dividend_details.append(dividend)
                else:
                    logger.warning(f"跳过无效的dividend值: {dividend}")

            if total_dividend > 0:
                merged['dividend'] = total_dividend
                logger.info(f"合并dividend: {symbol} {ex_div_date} -> 累加 {dividend_details} = {total_dividend}")
            else:
                merged['dividend'] = None
        else:
            merged['dividend'] = None

        # 处理description（公司名称）
        descriptions = [r.get('description', '') for r in records if r.get('description')]
        if descriptions:
            # 使用第一个非空描述，如果都不同则记录警告
            first_desc = descriptions[0]
            if len(set(descriptions)) > 1:
                logger.warning(f"描述不同，保留第一个: {descriptions}")
            merged['description'] = first_desc

        return merged

    def run(self):
        """运行监控流程"""
        logger.info("开始运行股票拆分和分红监控...")
        
        try:
            # 1. 获取股票拆分数据（当前和完整历史）
            logger.info("获取当前股票拆分数据...")
            current_splits_data = self.fetch_splits_data()
            if not current_splits_data:
                current_splits_data = []

            logger.info("获取完整历史股票拆分数据...")
            historical_splits_data = self.fetch_comprehensive_historical_splits()

            # 合并拆分数据
            all_splits_data = current_splits_data + historical_splits_data
            all_splits_data = self.deduplicate_splits_data(all_splits_data)

            # 2. 获取完整分红数据
            logger.info("获取完整分红数据...")
            dividends_data = self.fetch_dividends_data()

            # 3. 合并所有数据（已经是数据库格式）
            logger.info("合并所有数据...")
            all_db_records = all_splits_data + (dividends_data or [])
            logger.info(f"总计获取到 {len(all_db_records)} 条记录 (拆分: {len(all_splits_data)}, 分红: {len(dividends_data or [])})")
            
            # 4. 合并重复记录（基于symbol和ex_div_date）
            logger.info("合并重复记录...")
            merged_records = self.merge_duplicate_records(all_db_records)

            # 6. 保存到数据库
            logger.info("保存数据到数据库...")
            if not self.save_to_database(merged_records):
                logger.error("数据保存失败")
                return False
            
            # 7. 筛选今日数据并发送通知
            logger.info("筛选今日数据...")
            today_splits = self.filter_today_splits(all_splits_data)
            today_dividends = self.filter_today_dividends(dividends_data)

            # 8. 发送通知
            if today_splits or today_dividends:
                alert_message = self.format_alert_message(today_splits, today_dividends)
                self.send_alert(alert_message)
            else:
                logger.info("今日没有生效的股票拆分或分红")
            
            logger.info("股票拆分和分红监控流程完成")
            return True
            
        except Exception as e:
            logger.error(f"股票拆分和分红监控流程执行失败: {e}")
            logger.error(traceback.format_exc())
            return False


def main():
    """主函数"""
    # 配置日志
    log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
    logger.add(
        f"logs/{log_file_name}",
        level=0,  # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
        format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="00:00",  # rotation="10 MB"
        filter=__name__
    )
    
    monitor = TipranksMonitor()
    success = monitor.run()
    
    if success:
        logger.success("程序执行成功")
    else:
        logger.error("程序执行失败")

if __name__ == "__main__":
    main() 