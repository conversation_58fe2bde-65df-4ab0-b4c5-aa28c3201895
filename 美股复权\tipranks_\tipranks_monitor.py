#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import traceback
import requests
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional
from loguru import logger
import time
import re

# 导入企业微信通知模块
from utils.wecom_alert import WecomAlertManager
# 导入数据库管理模块
from utils.database_manager import db_manager, TipranksRehab
from peewee import *

class StockSplitsMonitor:
    """TipRanks股票拆分监控器"""
    
    def __init__(self):
        # 设置截止日期，只获取2025-08-15之后的数据
        self.cutoff_date = datetime(2025, 8, 15)
        # 股票拆分相关API
        self.splits_api_url = "https://www.tipranks.com/api/stocks/splits?sortDir=1&country=us&method=stockSplit&isFuture=true"        
        # 分红相关API  
        
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.tipranks.com/',
            'Origin': 'https://www.tipranks.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }
        self.session.headers.update(self.headers)
    
    def fetch_splits_data(self) -> List[Dict]:
        """获取当前股票拆分数据"""
        try:
            # 先访问主页获取cookies
            logger.info("先访问主页获取cookies...")
            main_page_response = self.session.get("https://www.tipranks.com/", timeout=30)
            if main_page_response.status_code == 200:
                logger.info("成功访问主页")
            else:
                logger.warning("访问主页失败，但继续尝试API")
            
            # 获取股票拆分数据
            logger.info(f"开始获取股票拆分数据: {self.splits_api_url}")
            response = self.session.get(self.splits_api_url, timeout=30)
            logger.info(f"API响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                
                if data and 'data' in data:
                    api_data = data['data']
                    logger.info(f"成功获取到 {len(api_data)} 条股票拆分记录")
                    
                    # 获取今天的日期作为上限
                    today = datetime.now()
                    logger.info(f"设置当前拆分数据日期上限: {today}")
                    
                    # 直接转换为数据库格式
                    db_records = []
                    for split in api_data:
                        try:
                            effective_date_str = split.get('effectiveDate')
                            if not effective_date_str:
                                continue
                            
                            effective_date = self.parse_effective_date(effective_date_str)
                            if not effective_date:
                                continue
                            
                            # 只保留不大于今天的数据
                            if effective_date <= today:
                                # 直接构建数据库格式的记录
                                db_record = {
                                    'symbol': split.get('ticker', ''),
                                    'description': split.get('companyName', ''),
                                    'ex_div_date': effective_date,
                                    'split_ratio': self.parse_split_ratio(split.get('splitRatio', '')),
                                }
                                
                                db_records.append(db_record)
                            
                        except Exception as e:
                            logger.warning(f"处理拆分记录失败: {split}, 错误: {e}")
                            continue
                    
                    logger.info(f"成功转换 {len(db_records)} 条拆分记录为数据库格式（过滤后）")
                    return db_records
                else:
                    logger.warning("API响应中没有找到数据")
                    return []
            else:
                logger.error(f"请求API失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"获取股票拆分数据失败: {e}")
            logger.error(traceback.format_exc())
            return []

    def fetch_comprehensive_historical_splits(self) -> List[Dict]:
        """获取完整的历史股票拆分数据"""
        try:
            logger.info("尝试通过API获取历史拆分数据...")
            
            logger.info(f"设置截止日期: {self.cutoff_date}")
            
            all_historical_data = []
            page = 1
            page_size = 50
            has_more_pages = True
            should_stop = False
            
            # 获取今天的日期作为上限
            today = datetime.now()
            logger.info(f"设置日期上限: {today}")
            
            while has_more_pages and not should_stop:
                # 构建分页API URL
                historical_api_url = f"https://www.tipranks.com/api/stocks/splits?sortDir=1&country=us&method=stockSplit&isFuture=false&page={page}&limit={page_size}"
                
                logger.info(f"获取历史数据第 {page} 页: {historical_api_url}")
                
                response = self.session.get(historical_api_url, timeout=30)
                logger.info(f"历史数据第 {page} 页API响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # 检查是否有数据
                    if 'data' not in data or not data['data']:
                        logger.info(f"第 {page} 页没有更多数据，停止获取")
                        break
                    
                    page_data = data['data']
                    current_page_count = len(page_data)
                    
                    # 检查这一页的日期范围
                    page_dates = []
                    for split in page_data:
                        try:
                            effective_date_str = split.get('effectiveDate')
                            if effective_date_str:
                                effective_date = self.parse_effective_date(effective_date_str)
                                if effective_date:
                                    page_dates.append(effective_date)
                        except:
                            continue
                    
                    if page_dates:
                        min_date = min(page_dates)
                        max_date = max(page_dates)
                        logger.info(f"第 {page} 页日期范围: {min_date} 到 {max_date}")
                        
                        # 如果这一页的最小日期都小于截止日期，说明后面的数据都更旧，可以停止
                        if min_date < self.cutoff_date:
                            logger.info(f"第 {page} 页最小日期 {min_date} 小于截止日期 {self.cutoff_date}，停止获取")
                            should_stop = True
                    
                    # 直接转换为数据库格式并过滤日期
                    for split in page_data:
                        try:
                            effective_date_str = split.get('effectiveDate')
                            if not effective_date_str:
                                continue
                            
                            effective_date = self.parse_effective_date(effective_date_str)
                            if not effective_date:
                                continue
                            
                            # 只保留指定日期之后且不大于今天的数据
                            if effective_date >= self.cutoff_date and effective_date <= today:
                                # 直接构建数据库格式的记录
                                db_record = {
                                    'symbol': split.get('ticker', ''),
                                    'description': split.get('companyName', ''),
                                    'ex_div_date': effective_date,
                                    'split_ratio': self.parse_split_ratio(split.get('splitRatio', '')),
                                }
                                
                                all_historical_data.append(db_record)
                            
                        except Exception as e:
                            logger.warning(f"处理历史拆分记录失败: {split}, 错误: {e}")
                            continue
                    
                    logger.info(f"第 {page} 页获取到 {current_page_count} 条记录，过滤后保留 {len([r for r in all_historical_data if r.get('ex_div_date') >= self.cutoff_date and r.get('ex_div_date') <= today])} 条")
                    
                    # 如果这一页的数据少于页面大小，说明已经是最后一页
                    if current_page_count < page_size:
                        logger.info(f"第 {page} 页数据量小于 {page_size}，已获取完所有历史数据")
                        has_more_pages = False
                    else:
                        page += 1
                        time.sleep(1)  # 避免请求过于频繁
                        
                else:
                    logger.error(f"获取历史数据第 {page} 页失败，状态码: {response.status_code}")
            
            # 最终过滤，确保只保留指定日期之后且不大于今天的数据
            filtered_data = [record for record in all_historical_data if record.get('ex_div_date') >= self.cutoff_date and record.get('ex_div_date') <= today]
            
            if filtered_data:
                logger.info(f"成功获取到 {len(filtered_data)} 条 {self.cutoff_date} 之后且不大于 {today} 的历史股票拆分记录")
                return filtered_data
            else:
                logger.warning(f"未获取到 {self.cutoff_date} 之后且不大于 {today} 的任何历史数据")
                return []
                
        except Exception as e:
            logger.error(f"获取完整历史股票拆分数据失败: {e}")
            logger.error(traceback.format_exc())
            return []


    def fetch_dividends_data(self) -> Optional[List[Dict]]:
        """获取分红数据 - 包括历史和未来数据"""
        try:
            logger.info("开始获取分红数据...")
            
            all_dividends_data = []
            
            # 获取从指定日期开始到今天的所有分红数据
            historical_dividends = self.fetch_comprehensive_dividends_data()
            if historical_dividends:
                all_dividends_data.extend(historical_dividends)
                logger.info(f"获取到 {len(historical_dividends)} 条历史分红数据")
            
            if all_dividends_data:
                logger.info(f"总计获取到 {len(all_dividends_data)} 条分红记录")
                return all_dividends_data
            else:
                logger.warning("未获取到任何分红数据")
                return None
                
        except Exception as e:
            logger.error(f"获取分红数据时发生错误: {e}")
            logger.error(traceback.format_exc())
            return None
    
    def fetch_comprehensive_dividends_data(self) -> List[Dict]:
        """获取从指定日期开始的所有分红数据"""
        try:
            logger.info("开始获取分红数据...")
            
            # 设置截止日期，只获取2025-08-15之后的数据
            self.cutoff_date = datetime(2025, 8, 15)
            logger.info(f"设置分红数据截止日期: {self.cutoff_date}")
            
            all_dividends = []
            
            # 从指定日期开始获取数据
            start_date = self.cutoff_date
            end_date = datetime.now()  # 到今天
            
            current_date = start_date
            total_days = (end_date - start_date).days + 1
            processed_days = 0
            
            logger.info(f"获取分红数据范围: {start_date} 到 {end_date} (共 {total_days} 天)")
            
            while current_date <= end_date:
                date_str = current_date.strftime('%Y-%m-%d')
                
                # 每处理10天显示一次进度
                if processed_days % 10 == 0:
                    progress = (processed_days / total_days) * 100
                    logger.info(f"分红数据获取进度: {progress:.1f}% ({processed_days}/{total_days}天)")
                
                daily_dividends = self.fetch_daily_dividends_by_date(date_str)
                if daily_dividends:
                    # 过滤日期，只保留指定日期之后的数据
                    filtered_dividends = []
                    for dividend in daily_dividends:
                        try:
                            ex_div_date = dividend.get('ex_div_date')
                            if ex_div_date:
                                # 解析日期进行比较
                                dividend_date = datetime.strptime(ex_div_date, '%Y-%m-%d')
                                if dividend_date >= self.cutoff_date:
                                    filtered_dividends.append(dividend)
                        except Exception as e:
                            logger.warning(f"过滤分红记录日期失败: {dividend}, 错误: {e}")
                            continue
                    
                    if filtered_dividends:
                        all_dividends.extend(filtered_dividends)
                        logger.debug(f"{date_str}: 获取到 {len(daily_dividends)} 条分红记录，过滤后保留 {len(filtered_dividends)} 条")
                
                current_date += timedelta(days=1)
                processed_days += 1
                
                # 每获取5天的数据后稍作休息，避免请求过于频繁
                if processed_days % 5 == 0:
                    time.sleep(0.3)
            
            logger.info(f"分红数据获取完成，总计获取 {len(all_dividends)} 条记录")
            return all_dividends
            
        except Exception as e:
            logger.error(f"获取全面分红数据时发生错误: {e}")
            logger.error(traceback.format_exc())
            return []
    
    def fetch_daily_dividends_by_date(self, date_str: str) -> List[Dict]:
        """根据日期获取当日的分红数据"""
        try:
            # 构建API URL
            api_url = f"https://www.tipranks.com/calendars/dividends/{date_str}/payload.json"
            
            response = self.session.get(api_url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                # 检查数据结构
                if 'data' not in data:
                    return []
                
                table_data = data['data'].get('tableData', [])
                if not table_data:
                    return []
                
                dividends_list = []
                for dividend_item in table_data:
                    try:
                        # 解析分红数据
                        ticker = dividend_item.get('ticker', '').strip()
                        name = dividend_item.get('name', '').strip()
                        dividend_info = dividend_item.get('dividend', {})
                        
                        # 提取分红金额和执行日期
                        amount = None
                        execution_date = date_str  # 默认使用请求的日期
                        
                        if isinstance(dividend_info, dict):
                            amount = dividend_info.get('amount', 0)
                            # 执行日期在dividend对象内部
                            execution_date_iso = dividend_info.get('executionDate', '')
                            if execution_date_iso:
                                # 转换ISO日期格式为YYYY-MM-DD
                                try:
                                    execution_date = execution_date_iso.split('T')[0]
                                except:
                                    execution_date = date_str
                        
                        # 只有当有有效数据时才添加记录
                        if ticker and amount is not None and amount > 0:
                            dividend_record = {
                                'symbol': ticker,
                                'description': name,
                                'ex_div_date': execution_date,
                                'dividend': float(amount)

                            }
                            dividends_list.append(dividend_record)
                            
                    except Exception as item_error:
                        logger.warning(f"解析分红记录失败 ({date_str}): {item_error}")
                        continue
                
                return dividends_list
                
            elif response.status_code == 404:
                # 404表示该日期没有数据，这是正常的
                return []
            else:
                logger.warning(f"获取 {date_str} 分红数据失败，状态码: {response.status_code}")
                return []
                
        except Exception as e:
            logger.warning(f"获取 {date_str} 分红数据时发生错误: {e}")
            return []
    

    
    def fetch_historical_dividends_data(self) -> Optional[List[Dict]]:
        """获取历史分红数据 - 支持分页"""
        try:
            logger.info("开始获取历史分红数据...")
            
            all_historical_dividends = []
            
            # 尝试不同的分红数据API
            historical_urls = [
                "https://www.tipranks.com/api/stocks/getCalendarData?period=historical&country=us&type=dividend",
                "https://www.tipranks.com/calendars/dividends/historical/payload.json",
                "https://www.tipranks.com/api/calendars/dividends?period=historical&country=us"
            ]
            
            for url in historical_urls:
                try:
                    logger.info(f"尝试获取历史分红数据: {url}")
                    response = self.session.get(url, timeout=30)
                    
                    if response.status_code == 200:
                        data = response.json()
                        
                        # 根据不同的API响应格式解析数据
                        historical_data = self.parse_historical_dividends_response(data)
                        if historical_data:
                            all_historical_dividends.extend(historical_data)
                            logger.info(f"从 {url} 获取到 {len(historical_data)} 条历史分红数据")
                            break  # 成功获取数据后退出循环
                    else:
                        logger.warning(f"历史分红API响应失败: {url}, 状态码: {response.status_code}")
                        
                except Exception as e:
                    logger.warning(f"尝试URL失败 {url}: {e}")
                    continue
            
            # 如果上述方法都失败，尝试爬取网页
            if not all_historical_dividends:
                logger.info("API方法失败，尝试爬取历史分红网页...")
                scraped_data = self.scrape_historical_dividends_from_web()
                if scraped_data:
                    all_historical_dividends.extend(scraped_data)
            
            return all_historical_dividends if all_historical_dividends else None
            
        except Exception as e:
            logger.error(f"获取历史分红数据时发生错误: {e}")
            return None
    
    def parse_historical_dividends_response(self, data: Dict) -> List[Dict]:
        """解析历史分红数据响应"""
        try:
            dividends_data = []
            
            # 处理不同的响应格式
            if 'data' in data:
                calendar_data = data['data']
                if isinstance(calendar_data, list):
                    # 格式1: data是数组
                    for item in calendar_data:
                        if isinstance(item, dict) and 'events' in item:
                            date_str = item.get('date', '')
                            for event in item.get('events', []):
                                if event.get('ticker') and event.get('dividend'):
                                    dividend_record = {
                                        'ticker': event.get('ticker', ''),
                                        'company': event.get('companyName', ''),
                                        'dividend': event.get('dividend', 0),
                                        'ex_div_date': date_str,
                                        'type': 'dividend'
                                    }
                                    dividends_data.append(dividend_record)
                elif isinstance(calendar_data, dict) and 'calendarData' in calendar_data:
                    # 格式2: data包含calendarData
                    for day_data in calendar_data.get('calendarData', []):
                        date_str = day_data.get('date', '')
                        for event in day_data.get('events', []):
                            if event.get('ticker') and event.get('dividend'):
                                dividend_record = {
                                    'ticker': event.get('ticker', ''),
                                    'company': event.get('companyName', ''),
                                    'dividend': event.get('dividend', 0),
                                    'ex_div_date': date_str,
                                    'type': 'dividend'
                                }
                                dividends_data.append(dividend_record)
            
            return dividends_data
            
        except Exception as e:
            logger.error(f"解析历史分红数据响应时发生错误: {e}")
            return []
    
    def scrape_historical_dividends_from_web(self) -> List[Dict]:
        """从网页爬取历史分红数据"""
        try:
            logger.info("开始从网页爬取历史分红数据...")
            
            # 访问历史分红页面
            historical_dividend_url = "https://www.tipranks.com/calendars/dividends/historical"
            response = self.session.get(historical_dividend_url, timeout=30)
            
            if response.status_code == 200:
                # 这里可以添加BeautifulSoup解析逻辑
                # 由于网页结构复杂，先返回空列表
                logger.info("历史分红网页访问成功，但需要进一步解析")
                return []
            else:
                logger.error(f"访问历史分红网页失败，状态码: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"爬取历史分红网页时发生错误: {e}")
            return []


    def parse_date_string(self, date_str: str) -> str:
        """解析日期字符串为ISO格式"""
        try:
            # 尝试解析各种日期格式
            formats = [
                '%Y-%m-%d',
                '%m/%d/%Y',
                '%d/%m/%Y',
                '%b %d, %Y',
                '%B %d, %Y'
            ]
            
            for fmt in formats:
                try:
                    parsed_date = datetime.strptime(date_str, fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue
            
            return date_str  # 如果都解析不了，返回原字符串
            
        except Exception as e:
            logger.debug(f"解析日期字符串失败: {date_str}, 错误: {e}")
            return date_str

    def deduplicate_splits_data(self, splits_data: List[Dict]) -> List[Dict]:
        """去重拆分数据"""
        seen = set()
        unique_data = []
        
        for split in splits_data:
            # 使用ticker和日期作为唯一标识
            key = (split.get('symbol', ''), split.get('ex_div_date', ''))
            if key not in seen:
                seen.add(key)
                unique_data.append(split)
        
        return unique_data

    def parse_effective_date(self, date_str: str) -> Optional[datetime]:
        """解析生效日期"""
        try:
            # API返回格式：2025-07-02T00:00:00
            return datetime.fromisoformat(date_str.replace('Z', '+00:00').replace('T00:00:00', ''))
        except Exception as e:
            logger.warning(f"解析日期失败: {date_str}, 错误: {e}")
            return None

    def parse_split_ratio(self, ratio_str: str) -> Optional[float]:
        """解析拆分比例"""
        try:
            if ratio_str is None or ratio_str == '未知比例':
                return None
            
            # 如果已经是数字类型，直接返回
            if isinstance(ratio_str, (int, float)):
                return float(ratio_str)
            
            # 转换为字符串处理
            ratio_str = str(ratio_str)
            
            # 处理格式如 "2:1", "3-for-1", "1:2" 等
            if ':' in ratio_str:
                parts = ratio_str.split(':')
                if len(parts) == 2:
                    numerator = float(parts[0].strip())
                    denominator = float(parts[1].strip())
                    return numerator / denominator
            elif '-for-' in ratio_str.lower():
                parts = ratio_str.lower().split('-for-')
                if len(parts) == 2:
                    numerator = float(parts[0].strip())
                    denominator = float(parts[1].strip())
                    return numerator / denominator
            else:
                # 直接转换为浮点数
                return float(ratio_str)
                
        except Exception as e:
            logger.warning(f"解析拆分比例失败: {ratio_str}, 错误: {e}")
            return None

    def save_to_database(self, db_records: List[Dict]) -> bool:
        """保存数据到数据库"""
        try:
            if not db_records:
                logger.info("没有数据需要保存")
                return True
            
            # 检查表是否存在，如果不存在则创建
            if not TipranksRehab.table_exists():
                logger.info("数据库表不存在，将创建新表")
                TipranksRehab.create_table()
            
            logger.info(f"开始保存 {len(db_records)} 条记录到数据库...")
            
            # 使用批量插入方式保存数据
            try:
                with db_manager.common_db.atomic():
                    for batch in chunked(db_records, 50):
                        TipranksRehab.insert_many(batch).on_conflict_replace().execute()

                logger.info("数据保存成功")
            except Exception as e:
                logger.error(f"批量插入数据失败: {e}")
                raise
            return True
            
        except Exception as e:
            logger.error(f"保存数据到数据库失败: {e}")
            logger.error(traceback.format_exc())
            return False

    def filter_today_splits(self, splits_data: List[Dict]) -> List[Dict]:
        """筛选今日生效的股票拆分"""
        today = datetime.now()
        today_splits = []
        
        logger.info(f"筛选 {today.date()} 生效的股票拆分记录")
        
        for split in splits_data:
            try:
                effective_date = split.get('ex_div_date')
                if not effective_date:
                    continue
                
                # 如果已经是datetime对象，直接使用；如果是字符串，则解析
                if isinstance(effective_date, str):
                    effective_date = self.parse_effective_date(effective_date)
                    if not effective_date:
                        continue
                
                # 比较日期
                if effective_date.date() == today.date():
                    today_splits.append(split)
                    
            except Exception as e:
                logger.error(f"处理股票拆分记录失败: {split}, 错误: {e}")
                continue
        
        logger.info(f"找到 {len(today_splits)} 条今日生效的股票拆分记录")
        return today_splits

    def filter_today_dividends(self, dividends_data: List[Dict]) -> List[Dict]:
        """筛选今日生效的分红"""
        today = datetime.now()
        today_dividends = []
        
        logger.info(f"筛选 {today.date()} 生效的分红记录")
        
        for dividend in dividends_data:
            try:
                ex_div_date = dividend.get('ex_div_date')
                if not ex_div_date:
                    continue
                
                # 如果已经是datetime对象，直接使用；如果是字符串，则解析
                if isinstance(ex_div_date, str):
                    effective_date = self.parse_effective_date(ex_div_date)
                    if not effective_date:
                        continue
                else:
                    effective_date = ex_div_date
                
                # 比较日期
                if effective_date.date() == today.date():
                    today_dividends.append(dividend)
                    
            except Exception as e:
                logger.error(f"处理分红记录失败: {dividend}, 错误: {e}")
                continue
        
        logger.info(f"找到 {len(today_dividends)} 条今日生效的分红记录")
        return today_dividends
    
    def format_alert_message(self, today_splits: List[Dict], today_dividends: List[Dict] = None) -> str:
        """格式化警报消息"""
        if not today_splits and not today_dividends:
            return ""
        
        message_lines = [
            f"TIPRANKS拆合股分红通知 ({datetime.now().strftime('%Y-%m-%d')})",
            ""
        ]
        
        # 处理拆分信息
        if today_splits:
            # 按拆分类型分组
            forward_splits = [s for s in today_splits if s.get('split_ratio')<1]
            reverse_splits = [s for s in today_splits if s.get('split_ratio')>1]
            
            message_lines.append(f"今日拆合股：拆股 {len(forward_splits)} 只，合股 {len(reverse_splits)} 只")
            
            # 先显示正向拆分
            if forward_splits:
                message_lines.append("")
                message_lines.append("拆股:")
                for i, split in enumerate(forward_splits, 1):
                    ticker = split.get('symbol', '未知')
                    company_name = split.get('description', '未知公司')
                    ratio = split.get('split_ratio', '未知比例')
                    
                    message_lines.append(
                        f"  {i}. {company_name}\n"
                        f"     股票代码: {ticker}\n"
                        f"     拆股比例: {ratio}"
                    )
            
            # 再显示反向合并
            if reverse_splits:
                message_lines.append("")
                message_lines.append("合股:")
                for i, split in enumerate(reverse_splits, 1):
                    ticker = split.get('symbol', '未知')
                    company_name = split.get('description', '未知公司')
                    ratio = split.get('split_ratio', '未知比例')
                    
                    message_lines.append(
                        f"  {i}. {company_name}\n"
                        f"     股票代码: {ticker}\n"
                        f"     合股比例: {ratio}"
                    )
        
        # 处理分红信息
        if today_dividends:
            if today_splits:
                message_lines.append("")
            
            message_lines.append(f"今日分红：{len(today_dividends)} 只股票")
            message_lines.append("")
            message_lines.append("分红:")
            
            for i, dividend in enumerate(today_dividends, 1):
                ticker = dividend.get('symbol', '未知')
                amount = dividend.get('dividend', '待查询')
                company_name = dividend.get('description', '未知公司')
                
                message_lines.append(
                    f"  {i}. {company_name}\n"
                    f"     股票代码: {ticker}\n"
                    f"     分红金额: {amount if amount is not None else '待查询'}"
                )
        
        # 添加数据统计信息
        message_lines.append("")
        
        return "\n".join(message_lines)
    
    def send_alert(self, message: str) -> bool:
        """发送企业微信警报"""
        if not message:
            logger.info("没有今日生效的股票拆分或分红，不发送通知")
            return True
            
        try:
            logger.info("发送企业微信通知...")
            logger.info(f"通知内容:\n{message}")
            
            manager = WecomAlertManager(key = 'ee0b6801-f2c5-4811-ba1f-227b543b3459')
            manager.start()
            manager.add_message(message)
            manager.stop()
            
            logger.info("企业微信通知发送成功")
            return True
            
        except Exception as e:
            logger.error(f"发送企业微信通知失败: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def run(self):
        """运行监控流程"""
        logger.info("开始运行股票拆分和分红监控...")
        
        try:
            # 1. 获取股票拆分数据（当前和完整历史）
            logger.info("获取当前股票拆分数据...")
            current_splits_data = self.fetch_splits_data()
            if not current_splits_data:
                current_splits_data = []
            logger.info("获取完整历史股票拆分数据...")
            historical_splits_data = self.fetch_comprehensive_historical_splits()
            # 合并拆分数据
            all_splits_data = current_splits_data + historical_splits_data
            all_splits_data = self.deduplicate_splits_data(all_splits_data)
            # 2. 获取完整分红数据
            logger.info("获取完整分红数据...")
            dividends_data = self.fetch_dividends_data()
            # 3. 合并所有数据（已经是数据库格式）
            logger.info("合并所有数据...")
            all_db_records = all_splits_data + (dividends_data or [])
            logger.info(f"总计获取到 {len(all_db_records)} 条记录 (拆分: {len(all_splits_data)}, 分红: {len(dividends_data or [])})")
            
            # 4. 保存到数据库
            logger.info("保存数据到数据库...")
            if not self.save_to_database(all_db_records):
                logger.error("数据保存失败")
                return False
            
            # 5. 筛选今日数据并发送通知
            logger.info("筛选今日数据...")
            today_splits = self.filter_today_splits(all_splits_data)
            today_dividends = self.filter_today_dividends(dividends_data)
            
            # 6. 发送通知
            if today_splits or today_dividends:
                alert_message = self.format_alert_message(today_splits, today_dividends)
                self.send_alert(alert_message)
            else:
                logger.info("今日没有生效的股票拆分或分红")
            
            logger.info("股票拆分和分红监控流程完成")
            return True
            
        except Exception as e:
            logger.error(f"股票拆分和分红监控流程执行失败: {e}")
            logger.error(traceback.format_exc())
            return False

def main():
    """主函数"""
    # 配置日志
    log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
    logger.add(
        f"logs/{log_file_name}",
        level=0,  # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
        format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="00:00",  # rotation="10 MB"
        filter=__name__
    )
    
    monitor = StockSplitsMonitor()
    success = monitor.run()
    
    if success:
        logger.success("程序执行成功")
    else:
        logger.error("程序执行失败")

if __name__ == "__main__":
    main() 