#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
企业微信机器人告警推送工具

使用方法:
from wecom_alert import report_we_alert, WecomAlertManager

# 基础文本消息（向后兼容）
success, msg = report_we_alert(
    text="测试消息",
    key="your-wecom-key-here"
)

# Markdown_v2消息（支持更丰富的格式）
success, msg = report_we_alert(
    text="",  # 可以为空
    key="your-wecom-key-here",
    markdown_v2_content="# 标题\n**加粗文本**\n- 列表项\n> 引用内容"
)

# 使用消息管理器（文本模式）
manager = WecomAlertManager()
manager.start()  # 启动消息处理线程
manager.add_message("测试消息")  # 添加消息到队列
manager.stop()  # 停止消息处理线程

# 使用消息管理器（markdown_v2模式）
manager = WecomAlertManager(use_markdown_v2=True)
manager.start()
manager.add_message("# 标题\n**重要通知**")
manager.stop()
"""

import traceback
import requests
import queue
import threading
import time
from typing import Optional, List
from collections import deque
import os
from loguru import logger
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)
# retention="30 days" # 保留30天的日志


import urllib.request, os

# def get_system_proxy() -> dict:
#     """获取系统代理设置"""
#     proxy_handler = urllib.request.ProxyHandler()
#     proxies = {}
#
#     # 从系统获取代理设置
#     for protocol in ['http', 'https']:
#         if proxy := proxy_handler.proxies.get(protocol):
#             proxies[protocol] = proxy
#             os.environ[f"{protocol}_proxy"] = proxy
#
#     return proxies
#
# proxies = get_system_proxy()

def report_we_alert(
    text: str,
    key: str = "ee0b6801-f2c5-4811-ba1f-227b543b3459",
    msgtype: Optional[str] = None,
    markdown_v2_content: Optional[str] = None
) -> tuple[bool, str]:
    """
    发送消息到企业微信机器人，发送的消息不能超过20条/分钟。

    参数:
        text (str): 要发送的文本消息（向后兼容）
        key (str): 企业微信机器人的webhook key
        msgtype (str, optional): 消息类型，支持 "text" 或 "markdown_v2"
        markdown_v2_content (str, optional): markdown_v2内容，最长4096字节

    返回:
        tuple[bool, str]: (是否成功, 成功/错误消息)
    """
    url = f"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={key}"

    # 确定消息类型，向后兼容
    if msgtype is None:
        msgtype = "markdown_v2" if markdown_v2_content is not None else "text"

    # 构建payload
    if msgtype == "markdown_v2":
        content = markdown_v2_content if markdown_v2_content is not None else text
        payload = {
            "msgtype": "markdown_v2",
            "markdown_v2": {
                "content": content  # markdown_v2内容，最长不超过4096个字节，必须是utf8编码
            }
        }
    else:  # 默认text类型
        payload = {
            "msgtype": "text",
            "text": {
                "content": text  # 文本内容，最长不超过2048个字节，必须是utf8编码
            }
        }
    
    try:
        response = requests.post(url, json=payload
                                 # , proxies=proxies
                                 )
        
        if response.status_code == 200:
            response_json = response.json()
            
            if response_json.get("errcode") == 0:
                return True, response_json.get("errmsg", "操作成功")
            else:
                return False, response_json.get("errmsg", "未知错误")
        else:
            return False, f"HTTP错误: {response.status_code}，{response.text}"
            
    except requests.RequestException as e:
        return False, f"请求异常: {str(e)}"
    except Exception as e:
        return False, f"未知错误: {str(e)}\n{traceback.format_exc()}"

class WecomAlertManager:
    """企业微信消息管理器，处理消息队列和限流"""

    def __init__(self, key: str = "ee0b6801-f2c5-4811-ba1f-227b543b3459", interval: int = 10, use_markdown_v2: bool = False):
        """初始化消息管理器

        Args:
            key: 企业微信机器人的webhook key
            interval: 消息处理间隔（秒），默认10秒
            use_markdown_v2: 是否使用markdown_v2格式发送消息
        """
        self.message_queue = deque()
        self.queue_lock = threading.Lock()  # 保护 deque 的线程安全
        self.is_stopping = False
        self.message_thread = None
        self.key = key
        self.interval = interval  # 消息处理间隔
        self.use_markdown_v2 = use_markdown_v2
        
    def start(self):
        """启动消息处理线程"""
        if self.message_thread is None or not self.message_thread.is_alive():
            self.is_stopping = False
            self.message_thread = threading.Thread(target=self._process_messages)
            self.message_thread.daemon = True
            self.message_thread.start()
            
    def stop(self):
        """停止消息处理线程，等待所有消息处理完成"""
        self.is_stopping = True
        if self.message_thread:
            self.message_thread.join()
            
    def add_message(self, message: str, priority: bool = False):
        """添加消息到队列，自动处理超长消息

        Args:
            message: 要添加的消息
            priority: 是否优先处理（插队到队列前面）
        """
        if not message.strip():
            return

        # 根据消息类型确定大小限制
        max_bytes = 4096 if self.use_markdown_v2 else 2048

        # 处理超长消息，无需后缀标记
        message_parts = self._split_message(message, max_bytes)

        with self.queue_lock:
            for part in message_parts:
                if priority:
                    self.message_queue.appendleft(part)
                else:
                    self.message_queue.append(part)

    def _split_message(self, text: str, max_bytes: int) -> List[str]:
        """智能分割消息，优先按行分割，必要时按字符分割"""
        if len(text.encode('utf-8')) <= max_bytes:
            return [text]

        parts = []
        remaining = text

        while remaining:
            if len(remaining.encode('utf-8')) <= max_bytes:
                parts.append(remaining)
                break

            # 尝试按行分割
            truncated, remaining = self._truncate_by_lines(remaining, max_bytes)

            if not truncated:
                # 单行超长，按字符强制分割
                truncated = self._truncate_by_chars(remaining, max_bytes)
                remaining = remaining[len(truncated):]

            parts.append(truncated)

        return parts

    def _truncate_by_lines(self, text: str, max_bytes: int) -> tuple[str, str]:
        """按行截断文本，智能保护 markdown 表格表头"""
        lines = text.split('\n')
        truncated_lines = []
        current_bytes = 0

        for i, line in enumerate(lines):
            line_with_newline = line + '\n' if i < len(lines) - 1 else line
            line_bytes = len(line_with_newline.encode('utf-8'))

            if current_bytes + line_bytes <= max_bytes:
                truncated_lines.append(line)
                current_bytes += line_bytes
            else:
                # 当前行放不下，检查是否需要保护表格表头
                if truncated_lines:
                    final_lines, remaining_lines = self._protect_table_header(
                        truncated_lines, lines[i:], max_bytes
                    )
                    return '\n'.join(final_lines), '\n'.join(remaining_lines)
                else:
                    # 第一行就超长，返回空字符串表示需要字符级分割
                    return "", text

        # 所有行都能放下
        return text, ""

    def _protect_table_header(self, truncated_lines: List[str], remaining_lines: List[str], max_bytes: int) -> tuple[List[str], List[str]]:
        """保护 markdown 表格表头完整性"""
        if not truncated_lines or not remaining_lines:
            return truncated_lines, remaining_lines

        # 检查截断点是否在表格中间
        last_line = truncated_lines[-1].strip()
        next_line = remaining_lines[0].strip() if remaining_lines else ""

        # 判断是否是表格分隔行（如：| --- | --- |）
        is_separator = (next_line.startswith('|') and
                       next_line.endswith('|') and
                       '---' in next_line)

        # 判断是否是表格数据行
        is_table_row = (last_line.startswith('|') and last_line.endswith('|') and
                       next_line.startswith('|') and next_line.endswith('|'))

        if is_separator and len(remaining_lines) >= 2:
            # 下一行是分隔行，说明当前截断在表头和分隔行之间
            # 需要把表头和分隔行都移到下一部分
            table_header = truncated_lines[-1]
            separator = remaining_lines[0]

            # 计算表头+分隔行的字节数
            header_content = table_header + '\n' + separator
            header_bytes = len(header_content.encode('utf-8'))

            # 如果下一部分能容纳表头+分隔行，则移动过去
            if header_bytes <= max_bytes:
                return truncated_lines[:-1], [table_header] + remaining_lines

        elif is_table_row and len(truncated_lines) >= 2:
            # 当前在表格数据行中间截断，检查是否有表头可以保护
            # 向前查找表头和分隔行
            header_idx = -1
            separator_idx = -1

            for j in range(len(truncated_lines) - 1, -1, -1):
                line = truncated_lines[j].strip()
                if line.startswith('|') and line.endswith('|') and '---' in line:
                    separator_idx = j
                    if j > 0:
                        prev_line = truncated_lines[j-1].strip()
                        if prev_line.startswith('|') and prev_line.endswith('|'):
                            header_idx = j - 1
                    break

            # 如果找到了完整的表头结构，将其移到下一部分
            if header_idx >= 0 and separator_idx >= 0:
                table_part = truncated_lines[header_idx:]
                table_content = '\n'.join(table_part)
                table_bytes = len(table_content.encode('utf-8'))

                # 如果下一部分能容纳表头，则移动过去
                if table_bytes <= max_bytes:
                    return truncated_lines[:header_idx], table_part + remaining_lines

        # 默认情况，不做特殊处理
        return truncated_lines, remaining_lines

    def _truncate_by_chars(self, text: str, max_bytes: int) -> str:
        """按字符截断文本，确保不超过字节限制"""
        if len(text.encode('utf-8')) <= max_bytes:
            return text

        # 二分查找最大安全长度
        left, right = 0, len(text)
        while left < right:
            mid = (left + right + 1) // 2
            if len(text[:mid].encode('utf-8')) <= max_bytes:
                left = mid
            else:
                right = mid - 1

        return text[:left]

    def _collect_messages(self) -> List[str]:
        """从队列中收集消息，确保拼接后不超过大小限制"""
        messages = []
        max_bytes = 4096 if self.use_markdown_v2 else 2048

        with self.queue_lock:
            while self.message_queue:
                message = self.message_queue.popleft()

                # 模拟添加这条消息后的完整内容
                test_messages = messages + [message]
                test_content = "\n".join(test_messages)
                test_bytes = len(test_content.encode('utf-8'))

                if test_bytes > max_bytes:
                    # 拼接后会超限，放回队列前面并结束收集
                    self.message_queue.appendleft(message)
                    break

                # 可以安全添加
                messages.append(message)

        return messages

    def _send_messages(self, messages: List[str]) -> bool:
        """发送消息列表，确保内容不超过限制"""
        if not messages:
            return True

        content = "\n".join(messages)

        # 验证内容长度（自证正确性）
        max_bytes = 4096 if self.use_markdown_v2 else 2048
        content_bytes = len(content.encode('utf-8'))

        if content_bytes > max_bytes:
            logger.error(f"消息内容超过限制: {content_bytes} > {max_bytes} 字节")
            return False

        try:
            if self.use_markdown_v2:
                success, error_msg = report_we_alert("", self.key, markdown_v2_content=content)
            else:
                success, error_msg = report_we_alert(content, self.key)

            if not success:
                logger.warning(f"发送消息失败: {error_msg}")

            return success

        except Exception as e:
            logger.error(f"发送消息异常: {str(e)}")
            return False

    def _process_messages(self):
        """处理消息队列"""
        while True:
            time.sleep(self.interval)

            # 收集消息（优先处理失败重试的消息）
            messages = self._collect_messages()

            if messages:
                success = self._send_messages(messages)
                if not success:
                    # 发送失败，将消息放回队列前面重试
                    with self.queue_lock:
                        for msg in reversed(messages):
                            self.message_queue.appendleft(msg)
            
            # 如果正在停止，检查是否还有待处理的消息
            if self.is_stopping:
                with self.queue_lock:
                    if not self.message_queue:
                        logger.info("消息处理线程正常退出，所有消息已处理完成")
                        break
                    else:
                        # 仍有消息未发出，记录日志但继续处理
                        queue_size = len(self.message_queue)
                        logger.warning(f"收到停止信号但仍有消息未发出 - 队列中消息数: {queue_size}")
                        # 不break，继续处理剩余消息

if __name__ == "__main__":
    # 测试示例 - 向后兼容性测试
    print("=== 向后兼容性测试 ===")
    success, msg = report_we_alert("这是一条测试消息")
    print(f"基础文本消息 - 发送结果: {'成功' if success else '失败'}")
    print(f"返回信息: {msg}")

    # 测试markdown_v2功能
    print("\n=== Markdown_v2功能测试 ===")
    markdown_content = """# 测试标题
## 二级标题
**加粗文本** 和 *斜体文本*

### 列表示例
- 无序列表 1
- 无序列表 2
  - 子列表 2.1
  - 子列表 2.2

1. 有序列表 1
2. 有序列表 2

### 引用和代码
> 这是一个引用
>> 二级引用

`行内代码`

```
代码块示例
def hello():
    print("Hello World")
```

### 链接和分割线
[企业微信官网](https://work.weixin.qq.com)

---

### 表格
| 姓名 | 部门 | 状态 |
| :--- | :---: | ---: |
| 张三 | 开发 | 在线 |
| 李四 | 测试 | 离线 |
"""

    success, msg = report_we_alert(
        text="",
        markdown_v2_content=markdown_content
    )
    print(f"Markdown_v2消息 - 发送结果: {'成功' if success else '失败'}")
    print(f"返回信息: {msg}")

    # 测试WecomAlertManager的markdown_v2模式
    print("\n=== WecomAlertManager Markdown_v2测试 ===")
    manager = WecomAlertManager(use_markdown_v2=True)
    manager.start()

    # 添加几条markdown格式的消息
    manager.add_message("# 第一条消息\n**重要通知**：系统维护开始")
    manager.add_message("# 第二条消息\n- 维护项目1\n- 维护项目2")
    manager.add_message("# 第三条消息\n> 预计维护时间：2小时")

    manager.stop()
    print("WecomAlertManager测试完成")
