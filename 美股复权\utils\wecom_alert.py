#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
企业微信机器人告警推送工具

使用方法:
from wecom_alert import report_we_alert, WecomAlertManager

# 基础文本消息（向后兼容）
success, msg = report_we_alert(
    text="测试消息",
    key="your-wecom-key-here"
)

# Markdown_v2消息（支持更丰富的格式）
success, msg = report_we_alert(
    text="",  # 可以为空
    key="your-wecom-key-here",
    markdown_v2_content="# 标题\n**加粗文本**\n- 列表项\n> 引用内容"
)

# 使用消息管理器（文本模式）
manager = WecomAlertManager()
manager.start()  # 启动消息处理线程
manager.add_message("测试消息")  # 添加消息到队列
manager.stop()  # 停止消息处理线程

# 使用消息管理器（markdown_v2模式）
manager = WecomAlertManager(use_markdown_v2=True)
manager.start()
manager.add_message("# 标题\n**重要通知**")
manager.stop()
"""

import traceback
import requests
import queue
import threading
import time
from typing import Optional, List
from collections import deque
import os
from loguru import logger
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)
# retention="30 days" # 保留30天的日志


import urllib.request, os

# def get_system_proxy() -> dict:
#     """获取系统代理设置"""
#     proxy_handler = urllib.request.ProxyHandler()
#     proxies = {}
#
#     # 从系统获取代理设置
#     for protocol in ['http', 'https']:
#         if proxy := proxy_handler.proxies.get(protocol):
#             proxies[protocol] = proxy
#             os.environ[f"{protocol}_proxy"] = proxy
#
#     return proxies
#
# proxies = get_system_proxy()

def report_we_alert(
    text: str,
    key: str = "ee0b6801-f2c5-4811-ba1f-227b543b3459",
    msgtype: Optional[str] = None,
    markdown_v2_content: Optional[str] = None
) -> tuple[bool, str]:
    """
    发送消息到企业微信机器人，发送的消息不能超过20条/分钟。

    参数:
        text (str): 要发送的文本消息（向后兼容）
        key (str): 企业微信机器人的webhook key
        msgtype (str, optional): 消息类型，支持 "text" 或 "markdown_v2"
        markdown_v2_content (str, optional): markdown_v2内容，最长4096字节

    返回:
        tuple[bool, str]: (是否成功, 成功/错误消息)
    """
    url = f"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={key}"

    # 确定消息类型，向后兼容
    if msgtype is None:
        msgtype = "markdown_v2" if markdown_v2_content is not None else "text"

    # 构建payload
    if msgtype == "markdown_v2":
        content = markdown_v2_content if markdown_v2_content is not None else text
        payload = {
            "msgtype": "markdown_v2",
            "markdown_v2": {
                "content": content  # markdown_v2内容，最长不超过4096个字节，必须是utf8编码
            }
        }
    else:  # 默认text类型
        payload = {
            "msgtype": "text",
            "text": {
                "content": text  # 文本内容，最长不超过2048个字节，必须是utf8编码
            }
        }
    
    try:
        response = requests.post(url, json=payload
                                 # , proxies=proxies
                                 )
        
        if response.status_code == 200:
            response_json = response.json()
            
            if response_json.get("errcode") == 0:
                return True, response_json.get("errmsg", "操作成功")
            else:
                return False, response_json.get("errmsg", "未知错误")
        else:
            return False, f"HTTP错误: {response.status_code}，{response.text}"
            
    except requests.RequestException as e:
        return False, f"请求异常: {str(e)}"
    except Exception as e:
        return False, f"未知错误: {str(e)}\n{traceback.format_exc()}"

class WecomAlertManager:
    """企业微信消息管理器，处理消息队列和限流"""

    def __init__(self, key: str = "ee0b6801-f2c5-4811-ba1f-227b543b3459", interval: int = 10, use_markdown_v2: bool = False):
        """初始化消息管理器

        Args:
            key: 企业微信机器人的webhook key
            interval: 消息处理间隔（秒），默认10秒
            use_markdown_v2: 是否使用markdown_v2格式发送消息
        """
        self.message_queue = deque()
        self.queue_lock = threading.Lock()  # 保护 deque 的线程安全
        self.is_stopping = False
        self.message_thread = None
        self.key = key
        self.interval = interval  # 消息处理间隔
        self.use_markdown_v2 = use_markdown_v2
        
    def start(self):
        """启动消息处理线程"""
        if self.message_thread is None or not self.message_thread.is_alive():
            self.is_stopping = False
            self.message_thread = threading.Thread(target=self._process_messages)
            self.message_thread.daemon = True
            self.message_thread.start()
            
    def stop(self):
        """停止消息处理线程，等待所有消息处理完成"""
        self.is_stopping = True
        if self.message_thread:
            self.message_thread.join()
            
    def add_message(self, message: str, priority: bool = False):
        """添加消息到队列，自动处理超长消息

        Args:
            message: 要添加的消息
            priority: 是否优先处理（插队到队列前面）
        """
        if not message.strip():
            return

        # 根据消息类型确定大小限制
        max_bytes = 4096 if self.use_markdown_v2 else 2048
        suffix = "...[续]"
        suffix_bytes = len(suffix.encode('utf-8'))

        # 处理超长消息
        message_parts = self._split_message(message, max_bytes - suffix_bytes)

        with self.queue_lock:
            for i, part in enumerate(message_parts):
                # 除了最后一部分，其他都加上续标记
                if i < len(message_parts) - 1:
                    part += suffix

                if priority:
                    self.message_queue.appendleft(part)
                else:
                    self.message_queue.append(part)

    def _split_message(self, text: str, max_bytes: int) -> List[str]:
        """智能分割消息，优先按行分割，必要时按字符分割"""
        if len(text.encode('utf-8')) <= max_bytes:
            return [text]

        parts = []
        remaining = text

        while remaining:
            if len(remaining.encode('utf-8')) <= max_bytes:
                parts.append(remaining)
                break

            # 尝试按行分割
            truncated, remaining = self._truncate_by_lines(remaining, max_bytes)

            if not truncated:
                # 单行超长，按字符强制分割
                truncated = self._truncate_by_chars(remaining, max_bytes)
                remaining = remaining[len(truncated):]

            parts.append(truncated)

        return parts

    def _truncate_by_lines(self, text: str, max_bytes: int) -> tuple[str, str]:
        """按行截断文本，保持 markdown 格式完整性"""
        lines = text.split('\n')
        truncated_lines = []
        current_bytes = 0

        for i, line in enumerate(lines):
            line_with_newline = line + '\n' if i < len(lines) - 1 else line
            line_bytes = len(line_with_newline.encode('utf-8'))

            if current_bytes + line_bytes <= max_bytes:
                truncated_lines.append(line)
                current_bytes += line_bytes
            else:
                # 当前行放不下
                if truncated_lines:
                    # 有已收集的行，返回它们
                    remaining_lines = lines[i:]
                    return '\n'.join(truncated_lines), '\n'.join(remaining_lines)
                else:
                    # 第一行就超长，返回空字符串表示需要字符级分割
                    return "", text

        # 所有行都能放下
        return text, ""

    def _truncate_by_chars(self, text: str, max_bytes: int) -> str:
        """按字符截断文本，确保不超过字节限制"""
        if len(text.encode('utf-8')) <= max_bytes:
            return text

        # 二分查找最大安全长度
        left, right = 0, len(text)
        while left < right:
            mid = (left + right + 1) // 2
            if len(text[:mid].encode('utf-8')) <= max_bytes:
                left = mid
            else:
                right = mid - 1

        return text[:left]

    def _collect_messages(self) -> List[str]:
        """从队列中收集消息，确保拼接后不超过大小限制"""
        messages = []
        max_bytes = 4096 if self.use_markdown_v2 else 2048

        while True:
            try:
                message = self.message_queue.get_nowait()

                # 模拟添加这条消息后的完整内容
                test_messages = messages + [message]
                test_content = "\n".join(test_messages)
                test_bytes = len(test_content.encode('utf-8'))

                if test_bytes > max_bytes:
                    # 拼接后会超限，放回队列并结束收集
                    self.message_queue.put(message)
                    break

                # 可以安全添加
                messages.append(message)

            except queue.Empty:
                break

        return messages
        
    def _process_messages(self):
        """处理消息队列"""
        pending_messages = []  # 用于存储待重试的消息
        
        while True:
            time.sleep(self.interval)
            
            # 先处理pending_messages中的消息
            if pending_messages:
                content = "\n".join(pending_messages)
                if self.use_markdown_v2:
                    success, error_msg = report_we_alert("", self.key, markdown_v2_content=content)
                else:
                    success, error_msg = report_we_alert(content, self.key)

                if success:
                    pending_messages.clear()
                else:
                    logger.warning(f"重试发送pending消息失败: {error_msg}")
                continue

            # 从队列收集新消息
            messages = self._collect_messages()

            # 如果有消息要发送
            if messages:
                content = "\n".join(messages)
                if self.use_markdown_v2:
                    success, error_msg = report_we_alert("", self.key, markdown_v2_content=content)
                else:
                    success, error_msg = report_we_alert(content, self.key)

                if not success:
                    # 发送失败，将消息加入待重试列表
                    logger.warning(f"发送新消息失败: {error_msg}")
                    pending_messages.extend(messages)
            
            # 如果正在停止，检查是否还有待处理的消息
            if self.is_stopping:
                if self.message_queue.empty() and not pending_messages:
                    logger.info("消息处理线程正常退出，所有消息已处理完成")
                    break
                else:
                    # 仍有消息未发出，记录日志但继续处理
                    queue_size = self.message_queue.qsize()
                    pending_size = len(pending_messages)
                    logger.warning(f"收到停止信号但仍有消息未发出 - 队列中消息数: {queue_size}, 待重试消息数: {pending_size}")
                    # 不break，继续处理剩余消息

if __name__ == "__main__":
    # 测试示例 - 向后兼容性测试
    print("=== 向后兼容性测试 ===")
    success, msg = report_we_alert("这是一条测试消息")
    print(f"基础文本消息 - 发送结果: {'成功' if success else '失败'}")
    print(f"返回信息: {msg}")

    # 测试markdown_v2功能
    print("\n=== Markdown_v2功能测试 ===")
    markdown_content = """# 测试标题
## 二级标题
**加粗文本** 和 *斜体文本*

### 列表示例
- 无序列表 1
- 无序列表 2
  - 子列表 2.1
  - 子列表 2.2

1. 有序列表 1
2. 有序列表 2

### 引用和代码
> 这是一个引用
>> 二级引用

`行内代码`

```
代码块示例
def hello():
    print("Hello World")
```

### 链接和分割线
[企业微信官网](https://work.weixin.qq.com)

---

### 表格
| 姓名 | 部门 | 状态 |
| :--- | :---: | ---: |
| 张三 | 开发 | 在线 |
| 李四 | 测试 | 离线 |
"""

    success, msg = report_we_alert(
        text="",
        markdown_v2_content=markdown_content
    )
    print(f"Markdown_v2消息 - 发送结果: {'成功' if success else '失败'}")
    print(f"返回信息: {msg}")

    # 测试WecomAlertManager的markdown_v2模式
    print("\n=== WecomAlertManager Markdown_v2测试 ===")
    manager = WecomAlertManager(use_markdown_v2=True)
    manager.start()

    # 添加几条markdown格式的消息
    manager.add_message("# 第一条消息\n**重要通知**：系统维护开始")
    manager.add_message("# 第二条消息\n- 维护项目1\n- 维护项目2")
    manager.add_message("# 第三条消息\n> 预计维护时间：2小时")

    manager.stop()
    print("WecomAlertManager测试完成")
